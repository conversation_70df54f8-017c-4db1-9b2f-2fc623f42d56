#!/usr/bin/env python3
"""
JWT 认证系统测试脚本

这个脚本演示了如何使用 JWT 认证系统：
1. 用户注册
2. 用户登录获取 Token
3. 使用 Token 访问受保护的 API
4. Token 刷新

运行前请确保 Django 服务器正在运行：
python manage.py runserver
"""

import requests
import json

# API 基础 URL
BASE_URL = "http://localhost:8000"

def test_user_registration():
    """测试用户注册"""
    print("=== 测试用户注册 ===")
    
    url = f"{BASE_URL}/auth/register/"
    data = {
        "username": "testuser_jwt",
        "email": "<EMAIL>",
        "password": "testpass123",
        "first_name": "JWT",
        "last_name": "测试用户"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    return response.status_code == 200

def test_user_login():
    """测试用户登录"""
    print("\n=== 测试用户登录 ===")
    
    url = f"{BASE_URL}/auth/token/pair/"
    data = {
        "username": "testuser_jwt",
        "password": "testpass123"
    }
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        tokens = response.json()
        print(f"Access Token: {tokens['access'][:50]}...")
        print(f"Refresh Token: {tokens['refresh'][:50]}...")
        return tokens
    else:
        print(f"登录失败: {response.json()}")
        return None

def test_protected_api(access_token):
    """测试受保护的 API"""
    print("\n=== 测试受保护的 API ===")
    
    # 测试获取用户信息
    url = f"{BASE_URL}/users/profile/"
    headers = {"Authorization": f"Bearer {access_token}"}
    
    response = requests.get(url, headers=headers)
    print(f"获取用户信息 - 状态码: {response.status_code}")
    if response.status_code == 200:
        print(f"用户信息: {response.json()}")
    else:
        print(f"错误: {response.json()}")
    
    # 测试获取策略列表
    url = f"{BASE_URL}/strategies/strategies/"
    response = requests.get(url, headers=headers)
    print(f"获取策略列表 - 状态码: {response.status_code}")
    if response.status_code == 200:
        strategies = response.json()
        print(f"策略数量: {len(strategies)}")
    else:
        print(f"错误: {response.json()}")

def test_token_refresh(refresh_token):
    """测试 Token 刷新"""
    print("\n=== 测试 Token 刷新 ===")
    
    url = f"{BASE_URL}/auth/token/refresh/"
    data = {"refresh": refresh_token}
    
    response = requests.post(url, json=data)
    print(f"状态码: {response.status_code}")
    
    if response.status_code == 200:
        new_tokens = response.json()
        print(f"新的 Access Token: {new_tokens['access'][:50]}...")
        if 'refresh' in new_tokens:
            print(f"新的 Refresh Token: {new_tokens['refresh'][:50]}...")
        return new_tokens
    else:
        print(f"刷新失败: {response.json()}")
        return None

def test_unauthorized_access():
    """测试未授权访问"""
    print("\n=== 测试未授权访问 ===")
    
    url = f"{BASE_URL}/strategies/strategies/"
    response = requests.get(url)
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")

def main():
    """主测试函数"""
    print("JWT 认证系统测试")
    print("=" * 50)
    
    # 测试未授权访问
    test_unauthorized_access()
    
    # 测试用户注册
    if not test_user_registration():
        print("用户注册失败，可能用户已存在，继续测试...")
    
    # 测试用户登录
    tokens = test_user_login()
    if not tokens:
        print("登录失败，无法继续测试")
        return
    
    access_token = tokens['access']
    refresh_token = tokens['refresh']
    
    # 测试受保护的 API
    test_protected_api(access_token)
    
    # 测试 Token 刷新
    new_tokens = test_token_refresh(refresh_token)
    if new_tokens:
        print("Token 刷新成功！")
    
    print("\n=== 测试完成 ===")
    print("JWT 认证系统工作正常！")

if __name__ == "__main__":
    try:
        main()
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到服务器")
        print("请确保 Django 服务器正在运行：python manage.py runserver")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
