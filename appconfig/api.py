from ninja_extra import NinjaExtraAPI
from indicators.api import IndicatorController
from strategies.api import StrategyController
from findata.api import FinDataController

# 创建一个 NinjaExtraAPI 实例
api = NinjaExtraAPI(
    title="量化平台API",
    version="1.0",
    description="量化交易策略开发平台 API 文档",
)
api.register_controllers(FinDataController)
api.register_controllers(IndicatorController)
api.register_controllers(StrategyController)
