# JWT 用户认证系统

本项目已集成 django-ninja-jwt 用户认证系统，为策略管理 API 提供完整的用户权限控制。

## 功能特性

### 🔐 JWT 认证
- 基于 JWT Token 的无状态认证
- Access Token (1小时有效期) + Refresh Token (7天有效期)
- 自动 Token 轮换和黑名单机制

### 👥 用户管理
- 用户注册、登录、个人信息管理
- 密码修改功能
- 用户权限控制

### 🛡️ 策略权限控制
- 用户只能访问自己创建的策略
- 超级用户可以访问所有策略
- 完整的 CRUD 权限验证

## API 端点

### 认证相关
```
POST /auth/token/pair/          # 获取 JWT Token (登录)
POST /auth/token/refresh/       # 刷新 Token
POST /auth/token/verify/        # 验证 Token
POST /auth/register/            # 用户注册
```

### 用户管理
```
GET  /users/profile/            # 获取用户信息
PUT  /users/profile/            # 更新用户信息
POST /users/change-password/    # 修改密码
```

### 策略管理 (需要认证)
```
GET    /strategies/strategies/           # 获取策略列表 (仅当前用户)
POST   /strategies/strategies/indicator/ # 创建指标策略
POST   /strategies/strategies/code/      # 创建代码策略
GET    /strategies/strategies/{id}/      # 获取策略详情
PUT    /strategies/strategies/{id}/      # 更新策略
DELETE /strategies/strategies/{id}/      # 删除策略
```

## 使用示例

### 1. 用户注册
```bash
curl -X POST http://localhost:8000/auth/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpass123",
    "first_name": "测试",
    "last_name": "用户"
  }'
```

### 2. 用户登录
```bash
curl -X POST http://localhost:8000/auth/token/pair/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass123"
  }'
```

响应示例：
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 3. 使用 Token 访问受保护的 API
```bash
curl -X GET http://localhost:8000/strategies/strategies/ \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
```

### 4. 刷新 Token
```bash
curl -X POST http://localhost:8000/auth/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }'
```

## 权限控制

### 策略访问权限
- **普通用户**: 只能访问自己创建的策略
- **超级用户**: 可以访问所有用户的策略
- **未认证用户**: 无法访问任何策略 API

### 执行任务权限
- 用户只能为自己的策略创建执行任务
- 只能查看和管理自己策略的执行任务
- 超级用户可以管理所有任务

## 开发和测试

### 创建测试用户
```bash
python manage.py create_test_users
```

这将创建以下测试用户：
- `testuser1` (密码: testpass123)
- `testuser2` (密码: testpass123)  
- `admin` (密码: admin123, 超级用户)

### 前端集成

在前端应用中，需要：

1. **存储 Token**
```javascript
// 登录后存储 Token
localStorage.setItem('access_token', response.access);
localStorage.setItem('refresh_token', response.refresh);
```

2. **请求拦截器**
```javascript
// 在每个请求中添加 Authorization 头
axios.defaults.headers.common['Authorization'] = 
  `Bearer ${localStorage.getItem('access_token')}`;
```

3. **Token 刷新**
```javascript
// 当 access token 过期时自动刷新
if (error.response.status === 401) {
  const refreshToken = localStorage.getItem('refresh_token');
  // 调用刷新 API
}
```

## 安全配置

### JWT 设置
- Access Token 有效期: 1小时
- Refresh Token 有效期: 7天
- 启用 Token 轮换和黑名单
- 使用 HS256 算法签名

### CORS 配置
- 允许前端域名跨域访问
- 支持 Authorization 头部
- 允许凭证传递

## 错误处理

### 常见错误码
- `401`: 未认证或 Token 无效
- `403`: 权限不足
- `404`: 资源不存在

### 错误响应格式
```json
{
  "error": "错误描述",
  "code": 401
}
```

## 部署注意事项

1. **生产环境**需要修改 `SECRET_KEY`
2. **CORS 配置**需要限制为实际的前端域名
3. **HTTPS** 部署时确保 Token 安全传输
4. **数据库迁移**确保用户表正确创建

## API 文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
