# Dockerfile for Strategy Execution
# This image is used for isolated strategy execution in containers

FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DJANGO_SETTINGS_MODULE=appconfig.settings

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Install Python dependencies
# Copy requirements first for better caching
COPY requirements.txt /app/
RUN pip install --no-cache-dir -r requirements.txt

# Install additional packages for strategy execution
RUN pip install --no-cache-dir \
    backtrader \
    talib-binary \
    numpy \
    pandas \
    matplotlib \
    psycopg2-binary

# Copy application code
COPY . /app/

# Create a non-root user for security
RUN useradd --create-home --shell /bin/bash strategy && \
    chown -R strategy:strategy /app

USER strategy

# Default command (will be overridden by container orchestrator)
CMD ["python", "manage.py", "run_strategy", "--help"]
