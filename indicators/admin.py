from django.contrib import admin
from .models import Indicator, CustomIndicator


@admin.register(Indicator)
class IndicatorAdmin(admin.ModelAdmin):
    list_display = ['class_name', 'display_name', 'source', 'category', 'created_at']
    list_filter = ['source', 'category']
    search_fields = ['class_name', 'display_name']
    ordering = ['category', 'class_name']


@admin.register(CustomIndicator)
class CustomIndicatorAdmin(admin.ModelAdmin):
    list_display = ['indicator', 'user', 'created_at']
    search_fields = ['indicator__class_name', 'indicator__display_name', 'user__username']
    ordering = ['indicator__class_name'] 