from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator

User = get_user_model()


class IndicatorSource(models.TextChoices):
    """指标来源枚举"""
    BACKTRADER = 'BT', 'Backtrader内置'
    TALIB = 'TA', 'Backtrader.TALib'
    CUSTOM = 'CU', '用户自定义'


class IndicatorCategory(models.TextChoices):
    """指标分类枚举"""
    # 趋势指标 - 识别价格趋势方向和强度
    TREND = 'TREND', '趋势指标'
    
    # 动量指标 - 测量价格变化的速度和强度
    MOMENTUM = 'MOMENTUM', '动量指标'
    
    # 震荡指标 - 识别超买超卖条件
    OSCILLATOR = 'OSCILLATOR', '震荡指标'
    
    # 成交量指标 - 分析成交量和价格关系
    VOLUME = 'VOLUME', '成交量指标'
    
    # 波动率指标 - 测量价格波动程度
    VOLATILITY = 'VOLATILITY', '波动率指标'
    
    # 支撑阻力指标 - 识别关键价格水平
    SUPPORT_RESISTANCE = 'SUPPORT_RESISTANCE', '支撑阻力指标'
    
    # 重叠指标 - 与价格重叠显示的指标
    OVERLAY = 'OVERLAY', '重叠指标'
    
    # 统计指标 - 统计和数学计算指标
    STATISTICAL = 'STATISTICAL', '统计指标'
    
    # 数学函数 - 基础数学运算
    MATH = 'MATH', '数学函数'
    
    # 价格变换 - 价格数据变换
    PRICE_TRANSFORM = 'PRICE_TRANSFORM', '价格变换'
    
    # 周期指标 - 识别市场周期
    CYCLE = 'CYCLE', '周期指标'
    
    # 模式识别 - 识别图表模式
    PATTERN = 'PATTERN', '模式识别'
    
    # 其他
    OTHER = 'OTHER', '其他'


class Indicator(models.Model):
    """指标基础模型"""
    class_name = models.CharField(max_length=100, unique=True, verbose_name="类名")
    display_name = models.CharField(max_length=100, verbose_name="显示名称")
    source = models.CharField(
        max_length=2,
        choices=IndicatorSource.choices,
        default=IndicatorSource.BACKTRADER,
        verbose_name="指标来源"
    )
    category = models.CharField(
        max_length=20,
        choices=IndicatorCategory.choices,
        default=IndicatorCategory.OTHER,
        verbose_name="指标分类"
    )
    description = models.TextField(blank=True, verbose_name="描述")
    required_lines = models.IntegerField(
        validators=[MinValueValidator(1)],
        default=1,
        verbose_name="所需数据线数量"
    )
    parameters = models.JSONField(
        default=dict,
        verbose_name="参数配置",
        help_text="格式：{'参数名': {'type': '类型', 'range': '范围', 'default': '默认值'}}"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "指标"
        verbose_name_plural = "指标"
        ordering = ['category', 'class_name']

    def __str__(self):
        return self.display_name


class CustomIndicator(models.Model):
    """用户自定义指标"""
    indicator = models.OneToOneField(
        Indicator,
        on_delete=models.CASCADE,
        related_name='custom_implementation',
        verbose_name="关联指标"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='custom_indicators',
        verbose_name="创建用户"
    )
    source_code = models.TextField(verbose_name="源代码")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "自定义指标"
        verbose_name_plural = "自定义指标"

    def __str__(self):
        return f"{self.indicator.display_name} - {self.user.username}" 