# Generated by Django 5.2 on 2025-06-19 01:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("indicators", "0001_initial"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="indicator",
            options={
                "ordering": ["category", "class_name"],
                "verbose_name": "指标",
                "verbose_name_plural": "指标",
            },
        ),
        migrations.AddField(
            model_name="indicator",
            name="category",
            field=models.CharField(
                choices=[
                    ("TREND", "趋势指标"),
                    ("MOMENTUM", "动量指标"),
                    ("OSCILLATOR", "震荡指标"),
                    ("VOLUME", "成交量指标"),
                    ("VOLATILITY", "波动率指标"),
                    ("SUPPORT_RESISTANCE", "支撑阻力指标"),
                    ("OVERLAY", "重叠指标"),
                    ("STATISTICAL", "统计指标"),
                    ("MATH", "数学函数"),
                    ("PRICE_TRANSFORM", "价格变换"),
                    ("CYCLE", "周期指标"),
                    ("PATTERN", "模式识别"),
                    ("OTHER", "其他"),
                ],
                default="OTHER",
                max_length=20,
                verbose_name="指标分类",
            ),
        ),
    ]
