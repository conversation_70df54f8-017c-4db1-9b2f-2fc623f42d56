# Generated by Django 5.2 on 2025-06-16 01:57

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Indicator",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "class_name",
                    models.CharField(max_length=100, unique=True, verbose_name="类名"),
                ),
                (
                    "display_name",
                    models.CharField(max_length=100, verbose_name="显示名称"),
                ),
                (
                    "source",
                    models.CharField(
                        choices=[
                            ("BT", "Backtrader内置"),
                            ("TA", "Backtrader.TALib"),
                            ("CU", "用户自定义"),
                        ],
                        default="BT",
                        max_length=2,
                        verbose_name="指标来源",
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="描述")),
                (
                    "required_lines",
                    models.IntegerField(
                        default=1,
                        validators=[django.core.validators.MinValueValidator(1)],
                        verbose_name="所需数据线数量",
                    ),
                ),
                (
                    "parameters",
                    models.JSONField(
                        default=dict,
                        help_text="格式：{'参数名': {'type': '类型', 'range': '范围', 'default': '默认值'}}",
                        verbose_name="参数配置",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "指标",
                "verbose_name_plural": "指标",
                "ordering": ["class_name"],
            },
        ),
        migrations.CreateModel(
            name="CustomIndicator",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("source_code", models.TextField(verbose_name="源代码")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_indicators",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建用户",
                    ),
                ),
                (
                    "indicator",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_implementation",
                        to="indicators.indicator",
                        verbose_name="关联指标",
                    ),
                ),
            ],
            options={
                "verbose_name": "自定义指标",
                "verbose_name_plural": "自定义指标",
            },
        ),
    ]
