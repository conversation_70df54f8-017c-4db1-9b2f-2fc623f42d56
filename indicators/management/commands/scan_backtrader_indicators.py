import inspect
import importlib
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from indicators.models import Indicator, IndicatorSource, IndicatorCategory


class Command(BaseCommand):
    help = '扫描 backtrader 代码，发现所有指标并写入数据库'
    
    # 指标分类规则 - 基于指标名称模式匹配
    CATEGORY_RULES = {
        # 趋势指标
        IndicatorCategory.TREND: [
            'SMA', 'EMA', 'WMA', 'DEMA', 'TEMA', 'HMA', 'KAMA', 'MAMA', 'FAMA',
            'T3', 'TRIMA', 'MAVP', 'MovingAverage', 'ExponentialMovingAverage',
            'WeightedMovingAverage', 'HullMovingAverage', 'KaufmanAdaptiveMovingAverage',
            'ADX', 'ADXR', 'DM', 'DI', 'DMI', 'DirectionalMovement', 'DirectionalIndicator',
            'PSAR', 'ParabolicSAR', 'SAR', 'AROON', 'AROONOSC', 'Aroon', 'AroonOscillator',
            'SUPERTREND', 'SuperTrend', 'TRIX', 'VWAP', 'VWMA'
        ],
        
        # 动量指标
        IndicatorCategory.MOMENTUM: [
            'RSI', 'RMI', 'CMO', 'RelativeStrengthIndex', 'RelativeMomentumIndex',
            'ChandeMomentumOscillator', 'ROC', 'RateOfChange', 'ROCP', 'ROCR', 'ROCR100',
            'MOMENTUM', 'Momentum', 'MACD', 'MACDHisto', 'MACDEXT', 'MacdHistogram',
            'PPO', 'APO', 'PriceOscillator', 'AbsolutePriceOscillator',
            'BOP', 'BalanceOfPower', 'PLUS_DM', 'PLUS_DI', 'MINUS_DM', 'MINUS_DI'
        ],
        
        # 震荡指标
        IndicatorCategory.OSCILLATOR: [
            'STOCH', 'STOCHF', 'STOCHRSI', 'Stochastic', 'StochasticFast', 'StochasticSlow',
            'StochasticRSI', 'CCI', 'CommodityChannelIndex', 'WILLR', 'WilliamsR',
            'TSI', 'TrueStrengthIndex', 'UO', 'ULTOSC', 'UltimateOscillator',
            'FISHER', 'FisherTransform', 'STOCHASTIC', 'KDJ'
        ],
        
        # 成交量指标
        IndicatorCategory.VOLUME: [
            'OBV', 'OnBalanceVolume', 'AD', 'ADLINE', 'AccumulationDistribution',
            'ADOSC', 'AccumulationDistributionOscillator', 'MFI', 'MoneyFlowIndex',
            'WILLAD', 'WilliamsAccumulationDistribution', 'PVI', 'NVI',
            'PositiveVolumeIndex', 'NegativeVolumeIndex', 'EMV', 'EaseOfMovement',
            'FORCE', 'ForceIndex', 'VOLSMA', 'VOLUME'
        ],
        
        # 波动率指标
        IndicatorCategory.VOLATILITY: [
            'ATR', 'AverageTrueRange', 'NATR', 'NormalizedAverageTrueRange',
            'TRANGE', 'TrueRange', 'STDDEV', 'StandardDeviation', 'VAR', 'Variance',
            'BBANDS', 'BollingerBands', 'KELTNER', 'KeltnerChannel', 'DC', 'DonchianChannel',
            'HV', 'HistoricalVolatility', 'CHAIKIN', 'ChaikinVolatility'
        ],
        
        # 支撑阻力指标
        IndicatorCategory.SUPPORT_RESISTANCE: [
            'PIVOT', 'PivotPoint', 'SUPPORT', 'RESISTANCE', 'FIBO', 'Fibonacci',
            'CAMARILLA', 'WOODIE', 'CLASSIC', 'DM', 'DEMARK'
        ],
        
        # 重叠指标 (与价格图表重叠显示)
        IndicatorCategory.OVERLAY: [
            'UPPERBAND', 'LOWERBAND', 'MIDDLEBAND', 'ENVELOPE', 'CHANNEL',
            'CLOUD', 'ICHIMOKU', 'IchimokuCloud', 'TENKAN', 'KIJUN', 'SENKOU',
            'CHIKOU', 'KUMO'
        ],
        
        # 统计指标
        IndicatorCategory.STATISTICAL: [
            'BETA', 'CORREL', 'Correlation', 'LINEARREG', 'LinearRegression',
            'LINEARREG_ANGLE', 'LINEARREG_INTERCEPT', 'LINEARREG_SLOPE',
            'TSF', 'TimeSeriesForecast', 'SLOPE', 'ZSCORE', 'ZScore'
        ],
        
        # 数学函数
        IndicatorCategory.MATH: [
            'ADD', 'DIV', 'MAX', 'MAXINDEX', 'MIN', 'MININDEX', 'MINMAX', 'MINMAXINDEX',
            'MULT', 'SUB', 'SUM', 'ABS', 'ACOS', 'ASIN', 'ATAN', 'CEIL', 'COS', 'COSH',
            'EXP', 'FLOOR', 'LN', 'LOG10', 'SIN', 'SINH', 'SQRT', 'TAN', 'TANH'
        ],
        
        # 价格变换
        IndicatorCategory.PRICE_TRANSFORM: [
            'AVGPRICE', 'AveragePrice', 'MEDPRICE', 'MedianPrice', 'TYPPRICE', 'TypicalPrice',
            'WCLPRICE', 'WeightedClosePrice', 'HT_DCPERIOD', 'HT_DCPHASE', 'HT_PHASOR',
            'HT_SINE', 'HT_TRENDMODE', 'HILBERT'
        ],
        
        # 周期指标
        IndicatorCategory.CYCLE: [
            'HT_DCPERIOD', 'HT_DCPHASE', 'HT_PHASOR', 'HT_SINE', 'HT_TRENDMODE',
            'MESA', 'DOMINANT', 'CYCLE', 'PERIOD'
        ],
        
        # 模式识别
        IndicatorCategory.PATTERN: [
            'CDL2CROWS', 'CDL3BLACKCROWS', 'CDL3INSIDE', 'CDL3LINESTRIKE', 'CDL3OUTSIDE',
            'CDL3STARSINSOUTH', 'CDL3WHITESOLDIERS', 'CDLABANDONEDBABY', 'CDLADVANCEBLOCK',
            'CDLBELTHOLD', 'CDLBREAKAWAY', 'CDLCLOSINGMARUBOZU', 'CDLCONCEALBABYSWALL',
            'CDLCOUNTERATTACK', 'CDLDARKCLOUDCOVER', 'CDLDOJI', 'CDLDOJISTAR',
            'CDLDRAGONFLYDOJI', 'CDLENGULFING', 'CDLEVENINGDOJISTAR', 'CDLEVENINGSTAR',
            'CDLGAPSIDESIDEWHITE', 'CDLGRAVESTONEDOJI', 'CDLHAMMER', 'CDLHANGINGMAN',
            'CDLHARAMI', 'CDLHARAMICROSS', 'CDLHIGHWAVE', 'CDLHIKKAKE', 'CDLHIKKAKEMOD',
            'CDLHOMINGPIGEON', 'CDLIDENTICAL3CROWS', 'CDLINNECK', 'CDLINVERTEDHAMMER',
            'CDLKICKING', 'CDLKICKINGBYLENGTH', 'CDLLADDERBOTTOM', 'CDLLONGLEGGEDDOJI',
            'CDLLONGLINE', 'CDLMARUBOZU', 'CDLMATCHINGLOW', 'CDLMATHOLD', 'CDLMORNINGDOJISTAR',
            'CDLMORNINGSTAR', 'CDLONNECK', 'CDLPIERCING', 'CDLRICKSHAWMAN', 'CDLRISEFALL3METHODS',
            'CDLSEPARATINGLINES', 'CDLSHOOTINGSTAR', 'CDLSHORTLINE', 'CDLSPINNINGTOP',
            'CDLSTALLEDPATTERN', 'CDLSTICKSANDWICH', 'CDLTAKURI', 'CDLTASUKIGAP',
            'CDLTHRUSTING', 'CDLTRISTAR', 'CDLUNIQUE3RIVER', 'CDLUPSIDEGAP2CROWS',
            'CDLXSIDEGAP3METHODS'
        ]
    }

    def determine_category(self, class_name):
        """根据指标名称确定分类"""
        # 先检查精确匹配
        for category, indicators in self.CATEGORY_RULES.items():
            if class_name in indicators:
                return category
        
        # 如果没有精确匹配，尝试部分匹配
        class_name_upper = class_name.upper()
        for category, indicators in self.CATEGORY_RULES.items():
            for indicator in indicators:
                if indicator.upper() in class_name_upper or class_name_upper in indicator.upper():
                    return category
        
        # 如果都没有匹配，返回其他
        return IndicatorCategory.OTHER

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示将要添加的指标，不实际写入数据库',
        )
        parser.add_argument(
            '--update',
            action='store_true',
            help='更新已存在的指标',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        update_existing = options['update']
        
        self.stdout.write(self.style.SUCCESS('开始扫描 backtrader 指标...'))
        
        # 扫描 backtrader.indicators
        bt_indicators = self.scan_backtrader_indicators()
        
        # 扫描 backtrader.talib
        talib_indicators = self.scan_talib_indicators()
        
        total_found = len(bt_indicators) + len(talib_indicators)
        self.stdout.write(f'发现 {len(bt_indicators)} 个 backtrader 内置指标')
        self.stdout.write(f'发现 {len(talib_indicators)} 个 talib 指标')
        self.stdout.write(f'总共发现 {total_found} 个指标')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('--dry-run 模式，不会实际写入数据库'))
            self.display_indicators('Backtrader 内置指标', bt_indicators)
            self.display_indicators('TALib 指标', talib_indicators)
            return
        
        # 写入数据库
        created_count = 0
        updated_count = 0
        
        with transaction.atomic():
            created, updated = self.save_indicators(bt_indicators, IndicatorSource.BACKTRADER, update_existing)
            created_count += created
            updated_count += updated
            
            created, updated = self.save_indicators(talib_indicators, IndicatorSource.TALIB, update_existing)
            created_count += created
            updated_count += updated
        
        self.stdout.write(
            self.style.SUCCESS(f'完成！创建了 {created_count} 个指标，更新了 {updated_count} 个指标')
        )

    def scan_backtrader_indicators(self):
        """扫描 backtrader.indicators 模块"""
        indicators = []
        
        try:
            import backtrader.indicators as bt_indicators
            
            # 获取模块中的所有成员
            for name in dir(bt_indicators):
                obj = getattr(bt_indicators, name)
                
                # 检查是否是指标类
                if self.is_indicator_class(obj):
                    indicator_info = self.extract_indicator_info(obj, name)
                    if indicator_info:
                        indicators.append(indicator_info)
                        
        except ImportError as e:
            self.stdout.write(
                self.style.ERROR(f'无法导入 backtrader.indicators: {e}')
            )
        
        return indicators

    def scan_talib_indicators(self):
        """扫描 backtrader.talib 模块"""
        indicators = []
        
        try:
            import backtrader.talib as bt_talib
            
            # 获取模块中的所有成员
            for name in dir(bt_talib):
                obj = getattr(bt_talib, name)
                
                # 检查是否是指标类
                if self.is_indicator_class(obj):
                    indicator_info = self.extract_indicator_info(obj, name)
                    if indicator_info:
                        indicators.append(indicator_info)
                        
        except ImportError as e:
            self.stdout.write(
                self.style.WARNING(f'无法导入 backtrader.talib: {e}')
            )
        
        return indicators

    def is_indicator_class(self, obj):
        """判断是否是指标类"""
        try:
            import backtrader as bt
            
            return (
                inspect.isclass(obj) and
                hasattr(obj, '__module__') and
                (obj.__module__.startswith('backtrader.indicators') or 
                 obj.__module__.startswith('backtrader.talib')) and
                issubclass(obj, bt.Indicator) and
                obj is not bt.Indicator  # 排除基类本身
            )
        except (ImportError, TypeError):
            return False

    def extract_indicator_info(self, indicator_class, class_name):
        """提取指标信息"""
        try:
            # 获取类的文档字符串作为描述
            description = indicator_class.__doc__ or ''
            if description:
                # 清理文档字符串，保留完整内容但格式化
                lines = [line.strip() for line in description.strip().split('\n')]
                # 移除空行并重新组合
                description = '\n'.join(line for line in lines if line)
            
            # 获取显示名称
            display_name = getattr(indicator_class, 'alias', [])
            if display_name and isinstance(display_name, list):
                display_name = display_name[0] if display_name else class_name
            else:
                display_name = class_name
            
            # 尝试获取参数信息
            parameters = self.extract_parameters(indicator_class)
            
            # 尝试获取所需数据线数量
            required_lines = getattr(indicator_class, 'datas', 1)
            if hasattr(required_lines, '__len__'):
                required_lines = len(required_lines)
            elif not isinstance(required_lines, int):
                required_lines = 1
            
            # 确定指标分类
            category = self.determine_category(class_name)
            
            return {
                'class_name': class_name,
                'display_name': display_name,
                'description': description,
                'parameters': parameters,
                'required_lines': required_lines,
                'category': category,
            }
            
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'提取指标 {class_name} 信息时出错: {e}')
            )
            return None

    def extract_parameters(self, indicator_class):
        """提取指标参数"""
        parameters = {}
        
        try:
            # 检查是否有 params 属性
            if hasattr(indicator_class, 'params'):
                params = indicator_class.params
                
                # 遍历参数
                for param_name in dir(params):
                    if not param_name.startswith('_'):
                        default_value = getattr(params, param_name)
                        
                        # 跳过函数类型的参数
                        if callable(default_value):
                            continue
                        
                        # 推断参数类型
                        param_type = type(default_value).__name__
                        if param_type == 'NoneType':
                            param_type = 'any'
                        
                        # 确保默认值可以序列化为 JSON
                        try:
                            import json
                            json.dumps(default_value)
                        except (TypeError, ValueError):
                            # 如果不能序列化，转换为字符串
                            default_value = str(default_value)
                            param_type = 'string'
                        
                        parameters[param_name] = {
                            'type': param_type,
                            'default': default_value,
                            'range': None  # backtrader 通常不提供范围信息
                        }
                        
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'提取参数信息时出错: {e}')
            )
        
        return parameters

    def save_indicators(self, indicators, source, update_existing=False):
        """保存指标到数据库"""
        created_count = 0
        updated_count = 0
        
        for indicator_info in indicators:
            try:
                indicator, created = Indicator.objects.get_or_create(
                    class_name=indicator_info['class_name'],
                    defaults={
                        'display_name': indicator_info['display_name'],
                        'source': source,
                        'category': indicator_info['category'],
                        'description': indicator_info['description'],
                        'parameters': indicator_info['parameters'],
                        'required_lines': indicator_info['required_lines'],
                    }
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'创建指标: {indicator_info["class_name"]}')
                elif update_existing:
                    # 更新现有指标
                    indicator.display_name = indicator_info['display_name']
                    indicator.source = source
                    indicator.category = indicator_info['category']
                    indicator.description = indicator_info['description']
                    indicator.parameters = indicator_info['parameters']
                    indicator.required_lines = indicator_info['required_lines']
                    indicator.save()
                    updated_count += 1
                    self.stdout.write(f'更新指标: {indicator_info["class_name"]}')
                else:
                    self.stdout.write(f'跳过已存在的指标: {indicator_info["class_name"]}')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'保存指标 {indicator_info["class_name"]} 时出错: {e}')
                )
        
        return created_count, updated_count

    def display_indicators(self, title, indicators):
        """显示指标列表"""
        self.stdout.write(f'\n{title}:')
        self.stdout.write('-' * 50)
        
        for indicator in indicators:
            params_count = len(indicator['parameters'])
            category_display = indicator['category'].label if hasattr(indicator['category'], 'label') else str(indicator['category'])
            self.stdout.write(
                f'  {indicator["class_name"]} ({indicator["display_name"]}) '
                f'- [{category_display}] {params_count} 个参数, {indicator["required_lines"]} 条数据线'
            )
            if indicator['description']:
                self.stdout.write(f'    描述: {indicator["description"][:100]}...') 