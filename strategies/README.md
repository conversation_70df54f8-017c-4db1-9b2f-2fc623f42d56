# Strategies App

这是一个用于管理交易策略的Django应用，支持两种类型的策略：基于指标的策略和代码策略。

## 功能特性

### 1. 策略类型

#### 基于指标的策略
- 选择一个或多个技术指标
- 设置指标条件（大于、小于、等于、介于、穿越等）
- 组合多个指标条件作为开仓和平仓信号
- 支持复杂的逻辑组合（AND/OR）

#### 代码策略
- 用户自定义基于Backtrader框架的策略代码
- 支持参数配置
- 完全的策略逻辑控制

### 2. 条件操作符

- `GT` - 大于
- `LT` - 小于
- `EQ` - 等于
- `GTE` - 大于等于
- `LTE` - 小于等于
- `BETWEEN` - 介于两值之间
- `CROSS_ABOVE` - 向上穿越
- `CROSS_BELOW` - 向下穿越

### 3. 逻辑操作符

- `AND` - 逻辑与（所有条件都必须满足）
- `OR` - 逻辑或（任一条件满足即可）

## 数据模型

### Strategy
策略基础模型，包含策略的基本信息。

### IndicatorStrategy
基于指标的策略实现，通过条件组和条件来定义策略逻辑。

### CodeStrategy
代码策略实现，包含用户自定义的策略代码。

### StrategyConditionGroup
策略条件组，用于组织和管理策略条件。

### StrategyCondition
具体的策略条件，定义指标比较逻辑。

### StrategyBacktest
策略回测记录，用于跟踪策略的回测结果。

## API接口

### 策略管理

- `GET /api/strategies/strategies` - 获取策略列表
- `GET /api/strategies/strategies/{id}` - 获取策略详情
- `POST /api/strategies/strategies/indicator` - 创建指标策略
- `POST /api/strategies/strategies/code` - 创建代码策略
- `PUT /api/strategies/strategies/{id}` - 更新策略
- `DELETE /api/strategies/strategies/{id}` - 删除策略

### 枚举值

- `GET /api/strategies/enums` - 获取所有枚举值

### 回测管理

- `POST /api/strategies/strategies/{id}/backtest` - 创建回测任务
- `GET /api/strategies/strategies/{id}/backtests` - 获取策略回测记录

## 使用示例

### 1. 创建基于指标的策略

```python
from strategies.models import (
    Strategy, IndicatorStrategy, StrategyConditionGroup, 
    StrategyCondition, StrategyType, ConditionOperator, LogicalOperator
)
from indicators.models import Indicator

# 创建策略
strategy = Strategy.objects.create(
    name='SMA-RSI组合策略',
    description='基于SMA和RSI的组合策略',
    strategy_type=StrategyType.INDICATOR_BASED,
    user=user
)

# 创建指标策略实现
indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)

# 创建开仓条件组
entry_group = StrategyConditionGroup.objects.create(
    indicator_strategy=indicator_strategy,
    name='开仓条件组',
    is_entry=True,
    logical_operator=LogicalOperator.AND
)

# 添加SMA条件
sma_indicator = Indicator.objects.get(class_name='SimpleMovingAverage')
StrategyCondition.objects.create(
    condition_group=entry_group,
    indicator=sma_indicator,
    indicator_parameters={'period': 20},
    operator=ConditionOperator.GREATER_THAN,
    target_value=100.0
)

# 添加RSI条件
rsi_indicator = Indicator.objects.get(class_name='RelativeStrengthIndex')
StrategyCondition.objects.create(
    condition_group=entry_group,
    indicator=rsi_indicator,
    indicator_parameters={'period': 14},
    operator=ConditionOperator.GREATER_THAN,
    target_value=30.0
)
```

### 2. 创建代码策略

```python
strategy_code = """
import backtrader as bt

class Strategy(bt.Strategy):
    def __init__(self):
        self.sma = bt.indicators.SimpleMovingAverage(self.data.close, period=20)
    
    def next(self):
        if not self.position:
            if self.data.close[0] > self.sma[0]:
                self.buy()
        elif self.data.close[0] < self.sma[0]:
            self.sell()
"""

strategy = Strategy.objects.create(
    name='简单SMA策略',
    description='基于SMA的简单策略',
    strategy_type=StrategyType.CODE_BASED,
    user=user
)

CodeStrategy.objects.create(
    strategy=strategy,
    source_code=strategy_code,
    parameters={'period': {'type': 'int', 'default': 20}}
)
```

### 3. 使用API创建策略

```python
import requests

# 创建指标策略
payload = {
    "name": "测试指标策略",
    "description": "API测试策略",
    "condition_groups": [
        {
            "name": "开仓条件",
            "is_entry": True,
            "logical_operator": "AND",
            "conditions": [
                {
                    "indicator_id": 1,
                    "indicator_parameters": {"period": 20},
                    "operator": "GT",
                    "target_value": 100.0,
                    "order": 0
                }
            ],
            "order": 0
        }
    ]
}

response = requests.post(
    'http://localhost:8000/api/strategies/strategies/indicator',
    json=payload
)
```

## 策略执行引擎

`strategy_engine.py` 模块提供了将策略配置转换为可执行的Backtrader策略的功能：

- `IndicatorStrategyExecutor` - 基于指标的策略执行器
- `StrategyEngine` - 策略引擎，负责创建和运行策略

### 使用策略引擎

```python
from strategies.strategy_engine import StrategyEngine

# 创建策略实例
strategy_class = StrategyEngine.create_strategy_from_config(strategy_id=1)

# 运行回测
backtest_config = {
    'initial_cash': 100000.0,
    'commission': 0.001,
    'symbols': ['AAPL'],
    'start_date': '2023-01-01',
    'end_date': '2023-12-31'
}

result = StrategyEngine.run_backtest(strategy_id=1, backtest_config=backtest_config)
print(result)
```

## 测试

运行测试用例：

```bash
python manage.py test strategies
```

## 示例脚本

使用 `example_usage.py` 脚本创建示例策略：

```bash
python manage.py shell
>>> from strategies.example_usage import run_examples
>>> run_examples()
```

## 管理后台

在Django管理后台中可以直接管理策略：

- 策略列表和详情
- 条件组和条件管理
- 回测记录查看

访问：`http://localhost:8000/admin/`

## 注意事项

1. 指标策略需要依赖 `indicators` app 中定义的指标
2. 代码策略中用户代码必须定义名为 `Strategy` 的类
3. 策略执行需要配合数据源使用
4. 回测功能需要历史数据支持

## 扩展功能

- 策略性能分析
- 策略优化
- 实时交易信号
- 策略组合管理
- 风险管理模块 