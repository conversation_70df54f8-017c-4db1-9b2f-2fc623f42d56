"""
Django management command for executing strategies in isolated environments.

This command serves as the entry point for strategy execution within containers.
It accepts strategy ID and data source parameters, initializes the strategy engine,
and executes the strategy with proper logging and error handling.

Usage:
    python manage.py run_strategy --strategy-id 1 --data-source influxdb --symbols AAPL,MSFT --start-date 2024-01-01 --end-date 2024-12-31
"""

import json
import logging
import sys
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.conf import settings

from strategies.strategy_engine import StrategyEngine
from strategies.models import Strategy
from findata.models import MarketData, Symbol


class Command(BaseCommand):
    help = 'Execute a strategy with specified parameters'

    def add_arguments(self, parser):
        """Add command line arguments"""
        parser.add_argument(
            '--strategy-id',
            type=int,
            required=True,
            help='ID of the strategy to execute'
        )
        
        parser.add_argument(
            '--data-source',
            type=str,
            choices=['influxdb', 'timescaledb', 'csv'],
            default='timescaledb',
            help='Data source for market data (default: timescaledb)'
        )
        
        parser.add_argument(
            '--symbols',
            type=str,
            required=True,
            help='Comma-separated list of symbols to trade (e.g., AAPL,MSFT)'
        )
        
        parser.add_argument(
            '--start-date',
            type=str,
            required=True,
            help='Start date for backtest (YYYY-MM-DD format)'
        )
        
        parser.add_argument(
            '--end-date',
            type=str,
            required=True,
            help='End date for backtest (YYYY-MM-DD format)'
        )
        
        parser.add_argument(
            '--timeframe',
            type=str,
            default='1d',
            help='Data timeframe (default: 1d)'
        )
        
        parser.add_argument(
            '--initial-cash',
            type=float,
            default=100000.0,
            help='Initial cash for backtest (default: 100000.0)'
        )
        
        parser.add_argument(
            '--commission',
            type=float,
            default=0.001,
            help='Commission rate (default: 0.001)'
        )
        
        parser.add_argument(
            '--output-format',
            type=str,
            choices=['json', 'text'],
            default='json',
            help='Output format (default: json)'
        )
        
        parser.add_argument(
            '--log-level',
            type=str,
            choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
            default='INFO',
            help='Logging level (default: INFO)'
        )

    def handle(self, *args, **options):
        """Main command handler"""
        # Setup logging
        self.setup_logging(options['log_level'])
        
        try:
            # Parse and validate arguments
            config = self.parse_arguments(options)
            
            # Log execution start
            self.stdout.write(
                self.style.SUCCESS(f"Starting strategy execution with config: {json.dumps(config, indent=2)}")
            )
            
            # Execute strategy
            results = self.execute_strategy(config)
            
            # Output results
            self.output_results(results, options['output_format'])
            
            # Log successful completion
            self.stdout.write(
                self.style.SUCCESS("Strategy execution completed successfully")
            )
            
        except Exception as e:
            error_msg = f"Strategy execution failed: {str(e)}"
            self.stderr.write(self.style.ERROR(error_msg))
            self.stderr.write(self.style.ERROR(traceback.format_exc()))
            
            # Output error in requested format
            if options['output_format'] == 'json':
                error_result = {
                    "success": False,
                    "error": str(e),
                    "traceback": traceback.format_exc(),
                    "timestamp": timezone.now().isoformat()
                }
                self.stdout.write(json.dumps(error_result, indent=2))
            
            sys.exit(1)

    def setup_logging(self, log_level: str):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stderr)
            ]
        )

    def parse_arguments(self, options: Dict[str, Any]) -> Dict[str, Any]:
        """Parse and validate command arguments"""
        # Parse symbols
        symbols = [s.strip() for s in options['symbols'].split(',')]
        
        # Parse dates
        try:
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d').date()
        except ValueError as e:
            raise CommandError(f"Invalid date format: {e}")
        
        if start_date >= end_date:
            raise CommandError("Start date must be before end date")
        
        # Validate strategy exists
        try:
            strategy = Strategy.objects.get(id=options['strategy_id'])
        except Strategy.DoesNotExist:
            raise CommandError(f"Strategy with ID {options['strategy_id']} does not exist")
        
        if not strategy.is_active:
            raise CommandError(f"Strategy {strategy.id} is not active")
        
        return {
            'strategy_id': options['strategy_id'],
            'strategy': strategy,
            'data_source': options['data_source'],
            'symbols': symbols,
            'start_date': start_date,
            'end_date': end_date,
            'timeframe': options['timeframe'],
            'initial_cash': options['initial_cash'],
            'commission': options['commission']
        }

    def execute_strategy(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the strategy with given configuration"""
        strategy_id = config['strategy_id']
        
        # Create backtest configuration
        backtest_config = {
            'initial_cash': config['initial_cash'],
            'commission': config['commission'],
            'symbols': config['symbols'],
            'start_date': config['start_date'],
            'end_date': config['end_date'],
            'timeframe': config['timeframe'],
            'data_source': config['data_source']
        }
        
        # Log strategy execution start
        logging.info(f"Executing strategy {strategy_id} with config: {backtest_config}")
        
        # Run the backtest using the strategy engine
        results = StrategyEngine.run_backtest(strategy_id, backtest_config)
        
        # Add execution metadata
        results.update({
            'strategy_id': strategy_id,
            'strategy_name': config['strategy'].name,
            'execution_timestamp': timezone.now().isoformat(),
            'config': backtest_config
        })
        
        return results

    def output_results(self, results: Dict[str, Any], output_format: str):
        """Output execution results in specified format"""
        if output_format == 'json':
            self.stdout.write(json.dumps(results, indent=2, default=str))
        else:
            # Text format output
            self.stdout.write("=" * 50)
            self.stdout.write("STRATEGY EXECUTION RESULTS")
            self.stdout.write("=" * 50)
            
            if results.get('success'):
                self.stdout.write(f"Strategy: {results.get('strategy_name', 'Unknown')}")
                self.stdout.write(f"Initial Cash: ${results.get('initial_cash', 0):,.2f}")
                self.stdout.write(f"Final Value: ${results.get('final_value', 0):,.2f}")
                self.stdout.write(f"Profit/Loss: ${results.get('profit', 0):,.2f}")
                self.stdout.write(f"Return Rate: {results.get('return_rate', 0):.2f}%")
            else:
                self.stdout.write(f"Error: {results.get('error', 'Unknown error')}")
            
            self.stdout.write("=" * 50)
