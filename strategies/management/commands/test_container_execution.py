"""
Test command for container-based strategy execution

This command tests the complete workflow of container-based strategy execution:
1. Creates a test strategy
2. Submits an execution task via API
3. Monitors the execution progress
4. Verifies the results

Usage:
    python manage.py test_container_execution
"""

import json
import time
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.contrib.auth import get_user_model

from strategies.models import Strategy, CodeStrategy, StrategyExecutionTask
from strategies.container_orchestrator import ContainerOrchestrator
from strategies.container_monitor import get_monitor

User = get_user_model()


class Command(BaseCommand):
    help = 'Test container-based strategy execution workflow'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-container',
            action='store_true',
            help='Skip actual container execution (for testing without Podman)'
        )
        
        parser.add_argument(
            '--strategy-id',
            type=int,
            help='Use existing strategy ID instead of creating a new one'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("开始测试容器化策略执行工作流"))
        
        try:
            # 1. 创建或获取测试策略
            if options.get('strategy_id'):
                strategy = Strategy.objects.get(id=options['strategy_id'])
                self.stdout.write(f"使用现有策略: {strategy.name} (ID: {strategy.id})")
            else:
                strategy = self.create_test_strategy()
                self.stdout.write(f"创建测试策略: {strategy.name} (ID: {strategy.id})")
            
            # 2. 测试容器编排器
            if not options.get('skip_container'):
                self.test_container_orchestrator(strategy)
            
            # 3. 测试API工作流
            self.test_api_workflow(strategy, skip_container=options.get('skip_container'))
            
            # 4. 测试监控服务
            self.test_monitoring_service()
            
            self.stdout.write(self.style.SUCCESS("所有测试完成"))
            
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"测试失败: {str(e)}"))
            raise

    def create_test_strategy(self):
        """创建测试策略"""
        # 获取或创建测试用户
        user, created = User.objects.get_or_create(
            username='test_user',
            defaults={'email': '<EMAIL>'}
        )
        
        # 创建简单的测试策略
        test_code = '''
import backtrader as bt

class TestStrategy(bt.Strategy):
    def __init__(self):
        self.sma = bt.indicators.SimpleMovingAverage(self.data.close, period=20)
        
    def next(self):
        if not self.position:
            if self.data.close[0] > self.sma[0]:
                self.buy()
        else:
            if self.data.close[0] < self.sma[0]:
                self.sell()

Strategy = TestStrategy
'''
        
        # 创建策略记录
        strategy = Strategy.objects.create(
            name=f"测试策略_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            description="容器化执行测试策略",
            strategy_type='CODE',
            user=user,
            is_active=True
        )
        
        # 创建代码策略实现
        CodeStrategy.objects.create(
            strategy=strategy,
            source_code=test_code,
            parameters={}
        )
        
        return strategy

    def test_container_orchestrator(self, strategy):
        """测试容器编排器"""
        self.stdout.write("测试容器编排器...")
        
        orchestrator = ContainerOrchestrator()
        
        # 测试参数
        test_params = {
            'strategy_id': strategy.id,
            'data_source': 'timescaledb',
            'symbols': ['AAPL', 'MSFT'],
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'timeframe': '1d',
            'initial_cash': 100000.0,
            'commission': 0.001
        }
        
        try:
            # 创建并运行容器
            container_id = orchestrator.create_and_run_strategy(**test_params)
            self.stdout.write(f"容器已启动: {container_id}")
            
            # 等待一段时间
            time.sleep(5)
            
            # 检查容器状态
            status = orchestrator.get_container_status(container_id)
            self.stdout.write(f"容器状态: {status}")
            
            # 获取日志
            logs = orchestrator.get_container_logs(container_id, tail=50)
            self.stdout.write(f"容器日志 (前50行):\n{logs[:500]}...")
            
            # 停止容器
            success = orchestrator.stop_container(container_id)
            self.stdout.write(f"容器停止: {'成功' if success else '失败'}")
            
        except Exception as e:
            self.stderr.write(f"容器编排器测试失败: {str(e)}")
            raise

    def test_api_workflow(self, strategy, skip_container=False):
        """测试API工作流"""
        self.stdout.write("测试API工作流...")
        
        # 创建执行任务
        task_data = {
            'strategy_id': strategy.id,
            'name': f'API测试任务_{datetime.now().strftime("%H%M%S")}',
            'description': 'API工作流测试',
            'data_source': 'timescaledb',
            'symbols': ['AAPL'],
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'timeframe': '1d',
            'initial_cash': 50000.0,
            'commission': 0.001,
            'container_config': {}
        }
        
        if skip_container:
            # 直接创建任务记录，不启动容器
            task = StrategyExecutionTask.objects.create(
                strategy=strategy,
                name=task_data['name'],
                description=task_data['description'],
                data_source=task_data['data_source'],
                symbols=task_data['symbols'],
                start_date=datetime.strptime(task_data['start_date'], '%Y-%m-%d').date(),
                end_date=datetime.strptime(task_data['end_date'], '%Y-%m-%d').date(),
                timeframe=task_data['timeframe'],
                initial_cash=task_data['initial_cash'],
                commission=task_data['commission'],
                status='pending'
            )
            self.stdout.write(f"创建测试任务: {task.id} (跳过容器执行)")
        else:
            # 模拟API调用创建任务
            from strategies.api import StrategyController
            from strategies.api import CreateExecutionTaskSchema
            
            controller = StrategyController()
            
            # 创建请求对象模拟
            class MockRequest:
                def __init__(self):
                    self.user = strategy.user
            
            request = MockRequest()
            payload = CreateExecutionTaskSchema(**task_data)
            
            # 调用API
            result = controller.create_execution_task(request, payload)
            
            if result.get('success'):
                task_id = result['task_id']
                container_id = result['container_id']
                self.stdout.write(f"任务创建成功: Task ID {task_id}, Container ID {container_id}")
                
                # 监控任务状态
                self.monitor_task_progress(controller, request, task_id)
            else:
                self.stderr.write(f"任务创建失败: {result.get('error')}")

    def monitor_task_progress(self, controller, request, task_id, max_wait=60):
        """监控任务进度"""
        self.stdout.write(f"监控任务 {task_id} 进度...")
        
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            # 获取任务状态
            status_result = controller.get_task_status(request, task_id)
            
            if 'error' in status_result:
                self.stderr.write(f"获取状态失败: {status_result['error']}")
                break
            
            status = status_result['status']
            self.stdout.write(f"任务状态: {status}")
            
            if status in ['completed', 'failed', 'cancelled']:
                # 获取任务详情
                task_detail = controller.get_execution_task(request, task_id)
                self.stdout.write(f"任务完成: {json.dumps(task_detail, indent=2, default=str)}")
                
                # 获取日志
                logs_result = controller.get_task_logs(request, task_id)
                if logs_result.get('logs'):
                    self.stdout.write(f"任务日志:\n{logs_result['logs'][:500]}...")
                
                break
            
            time.sleep(5)
        else:
            self.stdout.write("任务监控超时")

    def test_monitoring_service(self):
        """测试监控服务"""
        self.stdout.write("测试监控服务...")
        
        monitor = get_monitor()
        stats = monitor.get_monitoring_stats()
        
        self.stdout.write(f"监控统计: {json.dumps(stats, indent=2)}")
        
        # 测试监控服务状态
        if stats.get('monitor_running'):
            self.stdout.write("监控服务正在运行")
        else:
            self.stdout.write("监控服务未运行")
