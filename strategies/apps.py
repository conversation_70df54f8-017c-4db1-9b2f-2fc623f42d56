from django.apps import AppConfig
import atexit
import os


class StrategiesConfig(AppConfig):
    default_auto_field = "django.db.models.BigAutoField"
    name = "strategies"
    verbose_name = "交易策略"

    def ready(self):
        # 避免在自动重载时多次执行
        if os.environ.get("RUN_MAIN") != "true":
            # 启动容器监控服务
            self._start_container_monitoring()

            # 注册应用关闭时的清理函数
            atexit.register(self._stop_container_monitoring)

    def _start_container_monitoring(self):
        """启动容器监控服务"""
        try:
            from .container_monitor import start_monitoring
            import threading
            import time

            def delayed_start():
                # 延迟启动，确保Django完全初始化
                time.sleep(3)
                try:
                    start_monitoring()
                    print("策略执行容器监控服务已启动")
                except Exception as e:
                    print(f"启动容器监控服务失败: {e}")

            # 在后台线程中启动监控
            monitor_thread = threading.Thread(target=delayed_start, daemon=True)
            monitor_thread.start()

        except Exception as e:
            print(f"初始化容器监控服务失败: {e}")

    def _stop_container_monitoring(self):
        """停止容器监控服务"""
        try:
            from .container_monitor import stop_monitoring
            stop_monitoring()
            print("策略执行容器监控服务已停止")
        except Exception as e:
            print(f"停止容器监控服务失败: {e}")
