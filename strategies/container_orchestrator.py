"""
Container Orchestration Service for Strategy Execution

This module provides container-based strategy execution using Podman.
It manages the lifecycle of containers, handles workspace mounting,
and provides task tracking through container IDs.

Key Features:
- Isolated strategy execution in Podman containers
- Workspace mounting for code access
- Container lifecycle management
- Task tracking via container IDs
- Log and result collection
"""

import json
import logging
import subprocess
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)


class ContainerOrchestratorError(Exception):
    """Base exception for container orchestrator errors"""
    pass


class ContainerNotFoundError(ContainerOrchestratorError):
    """Raised when a container is not found"""
    pass


class ContainerExecutionError(ContainerOrchestratorError):
    """Raised when container execution fails"""
    pass


class ContainerOrchestrator:
    """
    Manages Podman containers for strategy execution
    """
    
    def __init__(self):
        self.workspace_path = getattr(settings, 'BASE_DIR', '/app')
        self.container_image = getattr(settings, 'STRATEGY_CONTAINER_IMAGE', 'python:3.12-slim')
        self.container_network = getattr(settings, 'STRATEGY_CONTAINER_NETWORK', 'host')
        self.max_execution_time = getattr(settings, 'STRATEGY_MAX_EXECUTION_TIME', 3600)  # 1 hour
        
    def create_and_run_strategy(
        self,
        strategy_id: int,
        data_source: str,
        symbols: List[str],
        start_date: str,
        end_date: str,
        timeframe: str = '1d',
        initial_cash: float = 100000.0,
        commission: float = 0.001,
        **kwargs
    ) -> str:
        """
        Create and run a strategy execution container
        
        Args:
            strategy_id: ID of the strategy to execute
            data_source: Data source for market data
            symbols: List of trading symbols
            start_date: Start date for backtest (YYYY-MM-DD)
            end_date: End date for backtest (YYYY-MM-DD)
            timeframe: Data timeframe
            initial_cash: Initial cash for backtest
            commission: Commission rate
            **kwargs: Additional parameters
            
        Returns:
            str: Container ID that serves as task ID
            
        Raises:
            ContainerExecutionError: If container creation or execution fails
        """
        try:
            # Generate unique container name
            container_name = f"strategy-{strategy_id}-{uuid.uuid4().hex[:8]}"
            
            # Prepare command arguments
            cmd_args = [
                'python', 'manage.py', 'run_strategy',
                '--strategy-id', str(strategy_id),
                '--data-source', data_source,
                '--symbols', ','.join(symbols),
                '--start-date', start_date,
                '--end-date', end_date,
                '--timeframe', timeframe,
                '--initial-cash', str(initial_cash),
                '--commission', str(commission),
                '--output-format', 'json',
                '--log-level', 'INFO'
            ]
            
            # Build Podman command
            podman_cmd = self._build_podman_command(
                container_name=container_name,
                cmd_args=cmd_args,
                **kwargs
            )
            
            logger.info(f"Starting container {container_name} for strategy {strategy_id}")
            logger.debug(f"Podman command: {' '.join(podman_cmd)}")
            
            # Execute Podman command
            result = subprocess.run(
                podman_cmd,
                capture_output=True,
                text=True,
                timeout=30  # Timeout for container creation, not execution
            )
            
            if result.returncode != 0:
                error_msg = f"Failed to create container: {result.stderr}"
                logger.error(error_msg)
                raise ContainerExecutionError(error_msg)
            
            # Extract container ID from output
            container_id = result.stdout.strip()
            
            logger.info(f"Container {container_name} started with ID: {container_id}")
            return container_id
            
        except subprocess.TimeoutExpired:
            error_msg = "Container creation timed out"
            logger.error(error_msg)
            raise ContainerExecutionError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error creating container: {str(e)}"
            logger.error(error_msg)
            raise ContainerExecutionError(error_msg)

    def _build_podman_command(
        self,
        container_name: str,
        cmd_args: List[str],
        **kwargs
    ) -> List[str]:
        """
        Build the Podman command for strategy execution
        
        Args:
            container_name: Name for the container
            cmd_args: Command arguments to run inside container
            **kwargs: Additional container options
            
        Returns:
            List[str]: Complete Podman command
        """
        # Base Podman command
        podman_cmd = [
            'podman', 'run',
            '--detach',  # Run in background
            '--name', container_name,
            '--network', self.container_network,
            '--rm',  # Remove container when it exits
        ]
        
        # Mount workspace directory
        workspace_mount = f"{self.workspace_path}:/app:Z"
        podman_cmd.extend(['--volume', workspace_mount])
        
        # Set working directory
        podman_cmd.extend(['--workdir', '/app'])
        
        # Environment variables
        env_vars = {
            'DJANGO_SETTINGS_MODULE': 'appconfig.settings',
            'PYTHONPATH': '/app',
            'PYTHONUNBUFFERED': '1'
        }
        
        # Add custom environment variables
        custom_env = kwargs.get('environment', {})
        env_vars.update(custom_env)
        
        for key, value in env_vars.items():
            podman_cmd.extend(['--env', f"{key}={value}"])
        
        # Resource limits
        memory_limit = kwargs.get('memory_limit', '2g')
        cpu_limit = kwargs.get('cpu_limit', '2')
        
        podman_cmd.extend(['--memory', memory_limit])
        podman_cmd.extend(['--cpus', cpu_limit])
        
        # Security options
        podman_cmd.extend(['--security-opt', 'no-new-privileges'])
        
        # Add container image
        podman_cmd.append(self.container_image)
        
        # Add command arguments
        podman_cmd.extend(cmd_args)
        
        return podman_cmd

    def get_container_status(self, container_id: str) -> Dict[str, Any]:
        """
        Get the status of a container
        
        Args:
            container_id: Container ID
            
        Returns:
            Dict containing container status information
            
        Raises:
            ContainerNotFoundError: If container is not found
        """
        try:
            # Get container information
            cmd = ['podman', 'inspect', container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                if 'no such container' in result.stderr.lower():
                    raise ContainerNotFoundError(f"Container {container_id} not found")
                else:
                    raise ContainerExecutionError(f"Failed to inspect container: {result.stderr}")
            
            # Parse container information
            container_info = json.loads(result.stdout)[0]
            state = container_info['State']
            
            status_info = {
                'container_id': container_id,
                'status': state['Status'],
                'running': state['Running'],
                'exit_code': state.get('ExitCode'),
                'started_at': state.get('StartedAt'),
                'finished_at': state.get('FinishedAt'),
                'error': state.get('Error', ''),
            }
            
            return status_info
            
        except subprocess.TimeoutExpired:
            raise ContainerExecutionError("Container status check timed out")
        except json.JSONDecodeError as e:
            raise ContainerExecutionError(f"Failed to parse container info: {str(e)}")
        except Exception as e:
            raise ContainerExecutionError(f"Unexpected error checking container status: {str(e)}")

    def get_container_logs(self, container_id: str, tail: int = 100) -> str:
        """
        Get logs from a container
        
        Args:
            container_id: Container ID
            tail: Number of lines to retrieve from the end
            
        Returns:
            str: Container logs
            
        Raises:
            ContainerNotFoundError: If container is not found
        """
        try:
            cmd = ['podman', 'logs', '--tail', str(tail), container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                if 'no such container' in result.stderr.lower():
                    raise ContainerNotFoundError(f"Container {container_id} not found")
                else:
                    raise ContainerExecutionError(f"Failed to get container logs: {result.stderr}")
            
            return result.stdout
            
        except subprocess.TimeoutExpired:
            raise ContainerExecutionError("Container logs retrieval timed out")
        except Exception as e:
            raise ContainerExecutionError(f"Unexpected error getting container logs: {str(e)}")

    def stop_container(self, container_id: str, timeout: int = 30) -> bool:
        """
        Stop a running container
        
        Args:
            container_id: Container ID
            timeout: Timeout in seconds before force killing
            
        Returns:
            bool: True if successfully stopped
            
        Raises:
            ContainerNotFoundError: If container is not found
        """
        try:
            cmd = ['podman', 'stop', '--time', str(timeout), container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=timeout + 10)
            
            if result.returncode != 0:
                if 'no such container' in result.stderr.lower():
                    raise ContainerNotFoundError(f"Container {container_id} not found")
                else:
                    logger.warning(f"Failed to stop container gracefully: {result.stderr}")
                    # Try force kill
                    return self._force_kill_container(container_id)
            
            logger.info(f"Container {container_id} stopped successfully")
            return True
            
        except subprocess.TimeoutExpired:
            logger.warning(f"Container stop timed out, attempting force kill")
            return self._force_kill_container(container_id)
        except Exception as e:
            logger.error(f"Unexpected error stopping container: {str(e)}")
            return False

    def _force_kill_container(self, container_id: str) -> bool:
        """Force kill a container"""
        try:
            cmd = ['podman', 'kill', container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"Container {container_id} force killed")
                return True
            else:
                logger.error(f"Failed to force kill container: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error force killing container: {str(e)}")
            return False

    def cleanup_finished_containers(self, max_age_hours: int = 24) -> int:
        """
        Clean up finished containers older than specified age
        
        Args:
            max_age_hours: Maximum age in hours for finished containers
            
        Returns:
            int: Number of containers cleaned up
        """
        try:
            # List all containers with strategy prefix
            cmd = ['podman', 'ps', '-a', '--filter', 'name=strategy-', '--format', 'json']
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                logger.error(f"Failed to list containers: {result.stderr}")
                return 0
            
            if not result.stdout.strip():
                return 0
            
            containers = json.loads(result.stdout)
            cleaned_count = 0
            
            for container in containers:
                container_id = container['Id']
                created_at = datetime.fromisoformat(container['CreatedAt'].replace('Z', '+00:00'))
                age_hours = (timezone.now() - created_at).total_seconds() / 3600
                
                if age_hours > max_age_hours and container['State'] in ['exited', 'stopped']:
                    if self._remove_container(container_id):
                        cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} old containers")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error during container cleanup: {str(e)}")
            return 0

    def _remove_container(self, container_id: str) -> bool:
        """Remove a container"""
        try:
            cmd = ['podman', 'rm', container_id]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.debug(f"Removed container {container_id}")
                return True
            else:
                logger.warning(f"Failed to remove container {container_id}: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error removing container {container_id}: {str(e)}")
            return False
