"""
Container Monitoring Service for Strategy Execution

This module provides monitoring capabilities for container-based strategy execution.
It tracks container status, collects execution results, and manages task lifecycle.

Key Features:
- Periodic container status monitoring
- Automatic task status synchronization
- Result collection from container logs
- Cleanup of finished containers
- Health monitoring and alerting
"""

import json
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from django.conf import settings
from django.utils import timezone

from .models import StrategyExecutionTask
from .container_orchestrator import (
    ContainerOrchestrator, 
    ContainerNotFoundError, 
    ContainerExecutionError
)

logger = logging.getLogger(__name__)


class ContainerMonitor:
    """
    监控容器状态并同步任务状态
    """
    
    def __init__(self):
        self.orchestrator = ContainerOrchestrator()
        self.monitoring_interval = getattr(settings, 'CONTAINER_MONITOR_INTERVAL', 30)  # 30秒
        self.cleanup_interval = getattr(settings, 'CONTAINER_CLEANUP_INTERVAL', 3600)  # 1小时
        self.max_container_age_hours = getattr(settings, 'MAX_CONTAINER_AGE_HOURS', 24)  # 24小时
        
        self._monitoring_thread = None
        self._cleanup_thread = None
        self._stop_event = threading.Event()
        self._running = False
    
    def start_monitoring(self):
        """启动监控服务"""
        if self._running:
            logger.warning("Container monitor is already running")
            return
        
        self._running = True
        self._stop_event.clear()
        
        # 启动监控线程
        self._monitoring_thread = threading.Thread(
            target=self._monitoring_loop,
            name="ContainerMonitor",
            daemon=True
        )
        self._monitoring_thread.start()
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(
            target=self._cleanup_loop,
            name="ContainerCleanup",
            daemon=True
        )
        self._cleanup_thread.start()
        
        logger.info("Container monitoring service started")
    
    def stop_monitoring(self):
        """停止监控服务"""
        if not self._running:
            return
        
        self._running = False
        self._stop_event.set()
        
        # 等待线程结束
        if self._monitoring_thread and self._monitoring_thread.is_alive():
            self._monitoring_thread.join(timeout=10)
        
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=10)
        
        logger.info("Container monitoring service stopped")
    
    def _monitoring_loop(self):
        """监控循环"""
        logger.info("Container monitoring loop started")
        
        while not self._stop_event.is_set():
            try:
                self._check_running_tasks()
            except Exception as e:
                logger.error(f"Error in monitoring loop: {str(e)}")
            
            # 等待下一次检查
            self._stop_event.wait(self.monitoring_interval)
        
        logger.info("Container monitoring loop stopped")
    
    def _cleanup_loop(self):
        """清理循环"""
        logger.info("Container cleanup loop started")
        
        while not self._stop_event.is_set():
            try:
                self._cleanup_old_containers()
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}")
            
            # 等待下一次清理
            self._stop_event.wait(self.cleanup_interval)
        
        logger.info("Container cleanup loop stopped")
    
    def _check_running_tasks(self):
        """检查运行中的任务状态"""
        running_tasks = StrategyExecutionTask.objects.filter(
            status='running',
            container_id__isnull=False
        )
        
        for task in running_tasks:
            try:
                self._update_task_status(task)
            except Exception as e:
                logger.error(f"Error updating task {task.id} status: {str(e)}")
    
    def _update_task_status(self, task: StrategyExecutionTask):
        """更新单个任务的状态"""
        try:
            # 获取容器状态
            container_status = self.orchestrator.get_container_status(task.container_id)
            
            if container_status['running']:
                # 容器仍在运行，无需更新
                return
            
            # 容器已停止，检查退出状态
            if container_status['status'] == 'exited':
                exit_code = container_status.get('exit_code', -1)
                
                if exit_code == 0:
                    # 成功完成，收集结果
                    self._collect_task_results(task)
                else:
                    # 执行失败
                    error_msg = container_status.get('error', f"容器退出码: {exit_code}")
                    self._mark_task_failed(task, error_msg)
            else:
                # 其他状态（如killed）
                self._mark_task_failed(task, f"容器状态异常: {container_status['status']}")
                
        except ContainerNotFoundError:
            # 容器不存在，可能已被清理
            logger.warning(f"Container {task.container_id} for task {task.id} not found")
            self._mark_task_failed(task, "容器已不存在")
        except ContainerExecutionError as e:
            logger.error(f"Error checking container status for task {task.id}: {str(e)}")
    
    def _collect_task_results(self, task: StrategyExecutionTask):
        """收集任务执行结果"""
        try:
            # 获取容器日志
            logs = self.orchestrator.get_container_logs(task.container_id)
            task.logs = logs
            
            # 尝试从日志中解析JSON结果
            result = self._parse_result_from_logs(logs)
            
            if result:
                task.result = result
                task.mark_as_completed(result)
                logger.info(f"Task {task.id} completed successfully")
            else:
                task.mark_as_completed()
                logger.info(f"Task {task.id} completed (no result parsed)")
                
        except Exception as e:
            logger.error(f"Error collecting results for task {task.id}: {str(e)}")
            self._mark_task_failed(task, f"结果收集失败: {str(e)}")
    
    def _parse_result_from_logs(self, logs: str) -> Optional[Dict]:
        """从日志中解析JSON结果"""
        try:
            # 查找日志中的JSON输出
            lines = logs.strip().split('\n')
            
            for line in reversed(lines):  # 从后往前查找
                line = line.strip()
                if line.startswith('{') and line.endswith('}'):
                    try:
                        result = json.loads(line)
                        # 验证是否是策略执行结果
                        if 'strategy_id' in result or 'success' in result:
                            return result
                    except json.JSONDecodeError:
                        continue
            
            return None
            
        except Exception as e:
            logger.error(f"Error parsing result from logs: {str(e)}")
            return None
    
    def _mark_task_failed(self, task: StrategyExecutionTask, error_message: str):
        """标记任务为失败"""
        try:
            # 尝试获取日志
            if task.container_id:
                try:
                    logs = self.orchestrator.get_container_logs(task.container_id)
                    task.logs = logs
                except:
                    pass
            
            task.mark_as_failed(error_message)
            logger.warning(f"Task {task.id} marked as failed: {error_message}")
            
        except Exception as e:
            logger.error(f"Error marking task {task.id} as failed: {str(e)}")
    
    def _cleanup_old_containers(self):
        """清理旧的容器"""
        try:
            cleaned_count = self.orchestrator.cleanup_finished_containers(
                max_age_hours=self.max_container_age_hours
            )
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old containers")
                
        except Exception as e:
            logger.error(f"Error during container cleanup: {str(e)}")
    
    def get_monitoring_stats(self) -> Dict:
        """获取监控统计信息"""
        try:
            # 统计各状态的任务数量
            stats = {
                'total_tasks': StrategyExecutionTask.objects.count(),
                'pending_tasks': StrategyExecutionTask.objects.filter(status='pending').count(),
                'running_tasks': StrategyExecutionTask.objects.filter(status='running').count(),
                'completed_tasks': StrategyExecutionTask.objects.filter(status='completed').count(),
                'failed_tasks': StrategyExecutionTask.objects.filter(status='failed').count(),
                'cancelled_tasks': StrategyExecutionTask.objects.filter(status='cancelled').count(),
            }
            
            # 添加最近24小时的统计
            last_24h = timezone.now() - timedelta(hours=24)
            stats['tasks_last_24h'] = StrategyExecutionTask.objects.filter(
                created_at__gte=last_24h
            ).count()
            
            # 监控服务状态
            stats['monitor_running'] = self._running
            stats['monitor_interval'] = self.monitoring_interval
            stats['cleanup_interval'] = self.cleanup_interval
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting monitoring stats: {str(e)}")
            return {"error": str(e)}


# 全局监控实例
_monitor_instance = None


def get_monitor() -> ContainerMonitor:
    """获取全局监控实例"""
    global _monitor_instance
    if _monitor_instance is None:
        _monitor_instance = ContainerMonitor()
    return _monitor_instance


def start_monitoring():
    """启动容器监控"""
    monitor = get_monitor()
    monitor.start_monitoring()


def stop_monitoring():
    """停止容器监控"""
    monitor = get_monitor()
    monitor.stop_monitoring()
