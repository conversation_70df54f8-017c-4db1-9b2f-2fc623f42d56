from django.db import models
from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator, MaxValueValidator
from indicators.models import Indicator

User = get_user_model()


class StrategyType(models.TextChoices):
    """策略类型枚举"""
    INDICATOR_BASED = 'INDICATOR', '基于指标'
    CODE_BASED = 'CODE', '代码策略'


class ConditionOperator(models.TextChoices):
    """条件操作符枚举"""
    GREATER_THAN = 'GT', '大于'
    LESS_THAN = 'LT', '小于'
    EQUAL = 'EQ', '等于'
    GREATER_EQUAL = 'GTE', '大于等于'
    LESS_EQUAL = 'LTE', '小于等于'
    BETWEEN = 'BETWEEN', '介于'
    CROSS_ABOVE = 'CROSS_ABOVE', '向上穿越'
    CROSS_BELOW = 'CROSS_BELOW', '向下穿越'


class LogicalOperator(models.TextChoices):
    """逻辑操作符枚举"""
    AND = 'AND', '且'
    OR = 'OR', '或'


class Strategy(models.Model):
    """策略基础模型"""
    name = models.CharField(max_length=100, verbose_name="策略名称")
    description = models.TextField(blank=True, verbose_name="策略描述")
    strategy_type = models.CharField(
        max_length=10,
        choices=StrategyType.choices,
        verbose_name="策略类型"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='strategies',
        verbose_name="创建用户"
    )
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "策略"
        verbose_name_plural = "策略"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.get_strategy_type_display()})"


class CodeStrategy(models.Model):
    """代码策略"""
    strategy = models.OneToOneField(
        Strategy,
        on_delete=models.CASCADE,
        related_name='code_implementation',
        verbose_name="关联策略"
    )
    source_code = models.TextField(verbose_name="策略源代码")
    parameters = models.JSONField(
        default=dict,
        verbose_name="策略参数",
        help_text="格式：{'参数名': {'type': '类型', 'default': '默认值', 'description': '描述'}}"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "代码策略"
        verbose_name_plural = "代码策略"

    def __str__(self):
        return f"代码策略: {self.strategy.name}"


class IndicatorStrategy(models.Model):
    """指标策略"""
    strategy = models.OneToOneField(
        Strategy,
        on_delete=models.CASCADE,
        related_name='indicator_implementation',
        verbose_name="关联策略"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "指标策略"
        verbose_name_plural = "指标策略"

    def __str__(self):
        return f"指标策略: {self.strategy.name}"


class StrategyConditionGroup(models.Model):
    """策略条件组"""
    indicator_strategy = models.ForeignKey(
        IndicatorStrategy,
        on_delete=models.CASCADE,
        related_name='condition_groups',
        verbose_name="指标策略"
    )
    name = models.CharField(max_length=100, verbose_name="条件组名称")
    is_entry = models.BooleanField(verbose_name="是否为开仓条件", help_text="True为开仓条件，False为平仓条件")
    logical_operator = models.CharField(
        max_length=5,
        choices=LogicalOperator.choices,
        default=LogicalOperator.AND,
        verbose_name="组内逻辑关系"
    )
    order = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name="排序"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "策略条件组"
        verbose_name_plural = "策略条件组"
        ordering = ['order']

    def __str__(self):
        condition_type = "开仓" if self.is_entry else "平仓"
        return f"{self.indicator_strategy.strategy.name} - {condition_type}条件组: {self.name}"


class StrategyCondition(models.Model):
    """策略条件"""
    condition_group = models.ForeignKey(
        StrategyConditionGroup,
        on_delete=models.CASCADE,
        related_name='conditions',
        verbose_name="条件组"
    )
    indicator = models.ForeignKey(
        Indicator,
        on_delete=models.CASCADE,
        verbose_name="指标"
    )
    indicator_parameters = models.JSONField(
        default=dict,
        verbose_name="指标参数",
        help_text="指标的参数配置"
    )
    operator = models.CharField(
        max_length=15,
        choices=ConditionOperator.choices,
        verbose_name="比较操作符"
    )
    target_value = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值"
    )
    target_value_min = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值最小值",
        help_text="用于BETWEEN操作符"
    )
    target_value_max = models.FloatField(
        null=True,
        blank=True,
        verbose_name="目标值最大值",
        help_text="用于BETWEEN操作符"
    )
    compare_indicator = models.ForeignKey(
        Indicator,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='compare_conditions',
        verbose_name="比较指标",
        help_text="用于指标间比较，如穿越等"
    )
    compare_indicator_parameters = models.JSONField(
        default=dict,
        verbose_name="比较指标参数",
        help_text="比较指标的参数配置"
    )
    order = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0)],
        verbose_name="排序"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "策略条件"
        verbose_name_plural = "策略条件"
        ordering = ['order']

    def __str__(self):
        return f"{self.condition_group} - {self.indicator.display_name} {self.get_operator_display()}"


class StrategyBacktest(models.Model):
    """策略回测记录"""
    strategy = models.ForeignKey(
        Strategy,
        on_delete=models.CASCADE,
        related_name='backtests',
        verbose_name="策略"
    )
    name = models.CharField(max_length=100, verbose_name="回测名称")
    symbols = models.JSONField(verbose_name="交易标的", help_text="交易标的列表")
    start_date = models.DateTimeField(verbose_name="开始时间")
    end_date = models.DateTimeField(verbose_name="结束时间")
    timeframe = models.CharField(max_length=20, verbose_name="时间周期")
    initial_cash = models.FloatField(
        default=100000.0,
        validators=[MinValueValidator(1000.0)],
        verbose_name="初始资金"
    )
    commission = models.FloatField(
        default=0.001,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="手续费率"
    )
    status = models.CharField(
        max_length=20,
        choices=[
            ('pending', '等待中'),
            ('running', '运行中'),
            ('completed', '已完成'),
            ('failed', '失败'),
        ],
        default='pending',
        verbose_name="状态"
    )
    result = models.JSONField(null=True, blank=True, verbose_name="回测结果")
    error_message = models.TextField(null=True, blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "策略回测"
        verbose_name_plural = "策略回测"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.strategy.name} - {self.name}"


class StrategyExecutionTask(models.Model):
    """策略执行任务模型 - 基于容器的执行"""

    STATUS_CHOICES = [
        ('pending', '等待中'),
        ('running', '运行中'),
        ('completed', '已完成'),
        ('failed', '失败'),
        ('cancelled', '已取消'),
    ]

    # 基础信息
    strategy = models.ForeignKey(
        Strategy,
        on_delete=models.CASCADE,
        related_name='execution_tasks',
        verbose_name="策略"
    )
    name = models.CharField(max_length=100, verbose_name="任务名称")
    description = models.TextField(blank=True, verbose_name="任务描述")

    # 容器信息
    container_id = models.CharField(
        max_length=64,
        unique=True,
        null=True,
        blank=True,
        verbose_name="容器ID",
        help_text="Podman容器ID，用作任务标识符"
    )
    container_name = models.CharField(
        max_length=100,
        null=True,
        blank=True,
        verbose_name="容器名称"
    )

    # 执行参数
    data_source = models.CharField(
        max_length=20,
        choices=[
            ('influxdb', 'InfluxDB'),
            ('timescaledb', 'TimescaleDB'),
            ('csv', 'CSV文件'),
        ],
        default='timescaledb',
        verbose_name="数据源"
    )
    symbols = models.JSONField(verbose_name="交易标的", help_text="交易标的列表")
    start_date = models.DateTimeField(verbose_name="开始时间")
    end_date = models.DateTimeField(verbose_name="结束时间")
    timeframe = models.CharField(max_length=20, default='1d', verbose_name="时间周期")
    initial_cash = models.FloatField(
        default=100000.0,
        validators=[MinValueValidator(1000.0)],
        verbose_name="初始资金"
    )
    commission = models.FloatField(
        default=0.001,
        validators=[MinValueValidator(0.0), MaxValueValidator(1.0)],
        verbose_name="手续费率"
    )

    # 执行状态
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='pending',
        verbose_name="状态"
    )

    # 时间信息
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    started_at = models.DateTimeField(null=True, blank=True, verbose_name="开始时间")
    finished_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")

    # 结果和日志
    result = models.JSONField(null=True, blank=True, verbose_name="执行结果")
    error_message = models.TextField(null=True, blank=True, verbose_name="错误信息")
    logs = models.TextField(null=True, blank=True, verbose_name="执行日志")

    # 容器配置
    container_config = models.JSONField(
        default=dict,
        verbose_name="容器配置",
        help_text="容器运行时配置参数"
    )

    class Meta:
        verbose_name = "策略执行任务"
        verbose_name_plural = "策略执行任务"
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['container_id']),
            models.Index(fields=['status']),
            models.Index(fields=['created_at']),
        ]

    def __str__(self):
        return f"{self.strategy.name} - {self.name} ({self.get_status_display()})"

    @property
    def duration(self):
        """计算执行时长"""
        if self.started_at and self.finished_at:
            return self.finished_at - self.started_at
        elif self.started_at:
            return timezone.now() - self.started_at
        return None

    @property
    def is_running(self):
        """检查任务是否正在运行"""
        return self.status == 'running'

    @property
    def is_finished(self):
        """检查任务是否已完成（成功或失败）"""
        return self.status in ['completed', 'failed', 'cancelled']

    def mark_as_running(self, container_id: str, container_name: str = None):
        """标记任务为运行中"""
        self.status = 'running'
        self.container_id = container_id
        self.container_name = container_name
        self.started_at = timezone.now()
        self.save(update_fields=['status', 'container_id', 'container_name', 'started_at'])

    def mark_as_completed(self, result: dict = None):
        """标记任务为已完成"""
        self.status = 'completed'
        self.finished_at = timezone.now()
        if result:
            self.result = result
        self.save(update_fields=['status', 'finished_at', 'result'])

    def mark_as_failed(self, error_message: str = None):
        """标记任务为失败"""
        self.status = 'failed'
        self.finished_at = timezone.now()
        if error_message:
            self.error_message = error_message
        self.save(update_fields=['status', 'finished_at', 'error_message'])

    def mark_as_cancelled(self):
        """标记任务为已取消"""
        self.status = 'cancelled'
        self.finished_at = timezone.now()
        self.save(update_fields=['status', 'finished_at'])
