# 策略执行容器化架构

## 概述

本系统实现了基于Podman容器的策略执行架构，提供了隔离、可扩展的策略回测环境。

## 架构组件

### 1. Django管理命令 (`run_strategy.py`)
- 策略执行的入口点
- 接受策略ID和数据源参数
- 在容器内执行策略引擎
- 支持JSON和文本输出格式

### 2. 容器编排服务 (`container_orchestrator.py`)
- 管理Podman容器生命周期
- 挂载工作空间目录到容器
- 提供容器状态查询和日志收集
- 支持容器资源限制和安全配置

### 3. 任务管理API (`api.py`)
- RESTful API端点用于任务生命周期管理
- 容器ID作为任务标识符
- 支持任务创建、状态查询、取消和日志获取

### 4. 数据库模型 (`models.py`)
- `StrategyExecutionTask`: 跟踪策略执行任务
- 存储容器信息、执行参数和结果
- 支持任务状态管理和历史记录

### 5. 容器监控服务 (`container_monitor.py`)
- 定期检查容器状态
- 自动同步任务状态
- 收集执行结果和日志
- 清理过期容器

## 部署指南

### 1. 安装Podman

```bash
# macOS
brew install podman

# Ubuntu/Debian
sudo apt-get install podman

# CentOS/RHEL
sudo dnf install podman
```

### 2. 构建策略执行镜像

```bash
# 构建Docker镜像
podman build -f Dockerfile.strategy -t strategy-executor:latest .
```

### 3. 配置Django设置

在 `settings.py` 中添加以下配置：

```python
# 策略执行容器配置
STRATEGY_CONTAINER_IMAGE = 'strategy-executor:latest'
STRATEGY_CONTAINER_NETWORK = 'host'
STRATEGY_MAX_EXECUTION_TIME = 3600  # 1小时

# 容器监控配置
CONTAINER_MONITOR_INTERVAL = 30  # 30秒
CONTAINER_CLEANUP_INTERVAL = 3600  # 1小时
MAX_CONTAINER_AGE_HOURS = 24  # 24小时
```

### 4. 运行数据库迁移

```bash
python manage.py migrate
```

### 5. 启动Django服务

```bash
python manage.py runserver
```

容器监控服务将自动启动。

## API使用示例

### 1. 创建策略执行任务

```bash
curl -X POST http://localhost:8000/strategies/execution-tasks \
  -H "Content-Type: application/json" \
  -d '{
    "strategy_id": 1,
    "name": "测试执行",
    "description": "API测试任务",
    "data_source": "timescaledb",
    "symbols": ["AAPL", "MSFT"],
    "start_date": "2024-01-01",
    "end_date": "2024-01-31",
    "timeframe": "1d",
    "initial_cash": 100000.0,
    "commission": 0.001
  }'
```

响应：
```json
{
  "success": true,
  "message": "策略执行任务已创建并启动",
  "task_id": 123,
  "container_id": "abc123def456"
}
```

### 2. 查询任务状态

```bash
curl http://localhost:8000/strategies/execution-tasks/123/status
```

### 3. 获取任务日志

```bash
curl http://localhost:8000/strategies/execution-tasks/123/logs?tail=100
```

### 4. 取消任务

```bash
curl -X POST http://localhost:8000/strategies/execution-tasks/123/cancel
```

### 5. 获取任务列表

```bash
curl http://localhost:8000/strategies/execution-tasks?status=running&limit=10
```

### 6. 获取监控统计

```bash
curl http://localhost:8000/strategies/execution-tasks/stats
```

## 管理命令

### 1. 直接执行策略

```bash
python manage.py run_strategy \
  --strategy-id 1 \
  --data-source timescaledb \
  --symbols AAPL,MSFT \
  --start-date 2024-01-01 \
  --end-date 2024-01-31 \
  --initial-cash 100000 \
  --commission 0.001 \
  --output-format json
```

### 2. 测试容器执行工作流

```bash
# 完整测试（需要Podman）
python manage.py test_container_execution

# 跳过容器执行的测试
python manage.py test_container_execution --skip-container

# 使用现有策略测试
python manage.py test_container_execution --strategy-id 1
```

## 安全考虑

1. **容器隔离**: 每个策略在独立容器中执行
2. **资源限制**: 配置内存和CPU限制
3. **非特权用户**: 容器内使用非root用户
4. **网络隔离**: 可配置容器网络策略
5. **代码沙箱**: 限制策略代码的系统访问

## 监控和日志

1. **实时监控**: 容器状态自动同步到数据库
2. **日志收集**: 自动收集容器执行日志
3. **错误处理**: 完善的错误捕获和报告
4. **清理机制**: 自动清理过期容器和任务

## 故障排除

### 1. 容器启动失败
- 检查Podman是否正确安装
- 验证镜像是否存在
- 检查工作空间目录权限

### 2. 任务状态不更新
- 检查监控服务是否运行
- 查看Django日志中的错误信息
- 验证容器ID是否有效

### 3. 日志获取失败
- 确认容器仍然存在
- 检查Podman日志命令权限
- 验证容器名称格式

## 扩展和定制

1. **自定义容器镜像**: 修改 `Dockerfile.strategy`
2. **添加数据源**: 扩展 `run_strategy` 命令
3. **监控集成**: 集成Prometheus/Grafana
4. **负载均衡**: 使用多个执行节点
5. **结果存储**: 自定义结果处理逻辑
