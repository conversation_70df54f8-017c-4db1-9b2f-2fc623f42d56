from django.contrib import admin
from .models import (
    Strategy, CodeStrategy, IndicatorStrategy, 
    StrategyConditionGroup, StrategyCondition, StrategyBacktest
)


class CodeStrategyInline(admin.StackedInline):
    model = CodeStrategy
    extra = 0


class IndicatorStrategyInline(admin.StackedInline):
    model = IndicatorStrategy
    extra = 0


@admin.register(Strategy)
class StrategyAdmin(admin.ModelAdmin):
    list_display = ['name', 'strategy_type', 'user', 'is_active', 'created_at']
    list_filter = ['strategy_type', 'is_active', 'created_at']
    search_fields = ['name', 'description', 'user__username']
    readonly_fields = ['created_at', 'updated_at']
    inlines = [CodeStrategyInline, IndicatorStrategyInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'description', 'strategy_type', 'user', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


class StrategyConditionInline(admin.TabularInline):
    model = StrategyCondition
    extra = 0
    fields = ['indicator', 'operator', 'target_value', 'target_value_min', 'target_value_max', 'compare_indicator', 'order']


@admin.register(StrategyConditionGroup)
class StrategyConditionGroupAdmin(admin.ModelAdmin):
    list_display = ['name', 'indicator_strategy', 'is_entry', 'logical_operator', 'order']
    list_filter = ['is_entry', 'logical_operator']
    search_fields = ['name', 'indicator_strategy__strategy__name']
    inlines = [StrategyConditionInline]


@admin.register(StrategyCondition)
class StrategyConditionAdmin(admin.ModelAdmin):
    list_display = ['condition_group', 'indicator', 'operator', 'target_value', 'order']
    list_filter = ['operator', 'condition_group__is_entry']
    search_fields = ['condition_group__name', 'indicator__display_name']


@admin.register(StrategyBacktest)
class StrategyBacktestAdmin(admin.ModelAdmin):
    list_display = ['name', 'strategy', 'status', 'start_date', 'end_date', 'created_at']
    list_filter = ['status', 'timeframe', 'created_at']
    search_fields = ['name', 'strategy__name']
    readonly_fields = ['created_at', 'updated_at', 'result']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'strategy', 'status')
        }),
        ('回测配置', {
            'fields': ('symbols', 'start_date', 'end_date', 'timeframe', 'initial_cash', 'commission')
        }),
        ('结果', {
            'fields': ('result', 'error_message'),
            'classes': ('collapse',)
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
