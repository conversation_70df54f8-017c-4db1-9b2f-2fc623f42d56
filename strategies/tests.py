from django.test import TestCase
from django.contrib.auth import get_user_model
from indicators.models import Indicator, IndicatorSource, IndicatorCategory
from .models import (
    Strategy, CodeStrategy, IndicatorStrategy, 
    StrategyConditionGroup, StrategyCondition,
    StrategyType, ConditionOperator, LogicalOperator
)

User = get_user_model()


class StrategyModelTests(TestCase):
    def setUp(self):
        """设置测试数据"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # 创建测试指标
        self.indicator_sma = Indicator.objects.create(
            class_name='SimpleMovingAverage',
            display_name='简单移动平均线',
            source=IndicatorSource.BACKTRADER,
            category=IndicatorCategory.TREND,
            description='简单移动平均线指标',
            parameters={'period': {'type': 'int', 'default': 20, 'range': '1-200'}}
        )
        
        self.indicator_rsi = Indicator.objects.create(
            class_name='RelativeStrengthIndex',
            display_name='相对强弱指数',
            source=IndicatorSource.BACKTRADER,
            category=IndicatorCategory.MOMENTUM,
            description='RSI指标',
            parameters={'period': {'type': 'int', 'default': 14, 'range': '1-100'}}
        )
    
    def test_create_indicator_strategy(self):
        """测试创建指标策略"""
        # 创建策略
        strategy = Strategy.objects.create(
            name='测试指标策略',
            description='基于SMA和RSI的测试策略',
            strategy_type=StrategyType.INDICATOR_BASED,
            user=self.user
        )
        
        # 创建指标策略实现
        indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)
        
        # 创建开仓条件组
        entry_group = StrategyConditionGroup.objects.create(
            indicator_strategy=indicator_strategy,
            name='开仓条件',
            is_entry=True,
            logical_operator=LogicalOperator.AND
        )
        
        # 添加SMA条件：价格 > SMA(20)
        StrategyCondition.objects.create(
            condition_group=entry_group,
            indicator=self.indicator_sma,
            indicator_parameters={'period': 20},
            operator=ConditionOperator.GREATER_THAN,
            target_value=100.0
        )
        
        # 添加RSI条件：RSI > 30
        StrategyCondition.objects.create(
            condition_group=entry_group,
            indicator=self.indicator_rsi,
            indicator_parameters={'period': 14},
            operator=ConditionOperator.GREATER_THAN,
            target_value=30.0
        )
        
        # 创建平仓条件组
        exit_group = StrategyConditionGroup.objects.create(
            indicator_strategy=indicator_strategy,
            name='平仓条件',
            is_entry=False,
            logical_operator=LogicalOperator.OR
        )
        
        # 添加RSI条件：RSI > 70
        StrategyCondition.objects.create(
            condition_group=exit_group,
            indicator=self.indicator_rsi,
            indicator_parameters={'period': 14},
            operator=ConditionOperator.GREATER_THAN,
            target_value=70.0
        )
        
        # 验证策略创建成功
        self.assertEqual(strategy.name, '测试指标策略')
        self.assertEqual(strategy.strategy_type, StrategyType.INDICATOR_BASED)
        self.assertTrue(hasattr(strategy, 'indicator_implementation'))
        
        # 验证条件组
        self.assertEqual(indicator_strategy.condition_groups.count(), 2)
        entry_conditions = entry_group.conditions.all()
        self.assertEqual(entry_conditions.count(), 2)
        
        # 验证条件
        sma_condition = entry_conditions.filter(indicator=self.indicator_sma).first()
        self.assertEqual(sma_condition.operator, ConditionOperator.GREATER_THAN)
        self.assertEqual(sma_condition.target_value, 100.0)
    
    def test_create_code_strategy(self):
        """测试创建代码策略"""
        strategy_code = """
import backtrader as bt

class Strategy(bt.Strategy):
    def __init__(self):
        self.sma = bt.indicators.SimpleMovingAverage(self.data.close, period=20)
    
    def next(self):
        if not self.position:
            if self.data.close[0] > self.sma[0]:
                self.buy()
        elif self.data.close[0] < self.sma[0]:
            self.sell()
"""
        
        # 创建策略
        strategy = Strategy.objects.create(
            name='测试代码策略',
            description='基于SMA的简单代码策略',
            strategy_type=StrategyType.CODE_BASED,
            user=self.user
        )
        
        # 创建代码策略实现
        code_strategy = CodeStrategy.objects.create(
            strategy=strategy,
            source_code=strategy_code,
            parameters={'period': {'type': 'int', 'default': 20, 'description': 'SMA周期'}}
        )
        
        # 验证策略创建成功
        self.assertEqual(strategy.name, '测试代码策略')
        self.assertEqual(strategy.strategy_type, StrategyType.CODE_BASED)
        self.assertTrue(hasattr(strategy, 'code_implementation'))
        self.assertEqual(code_strategy.source_code, strategy_code)
    
    def test_strategy_condition_operators(self):
        """测试策略条件操作符"""
        strategy = Strategy.objects.create(
            name='操作符测试策略',
            strategy_type=StrategyType.INDICATOR_BASED,
            user=self.user
        )
        
        indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)
        condition_group = StrategyConditionGroup.objects.create(
            indicator_strategy=indicator_strategy,
            name='测试条件组',
            is_entry=True
        )
        
        # 测试BETWEEN操作符
        between_condition = StrategyCondition.objects.create(
            condition_group=condition_group,
            indicator=self.indicator_rsi,
            operator=ConditionOperator.BETWEEN,
            target_value_min=30.0,
            target_value_max=70.0
        )
        
        self.assertEqual(between_condition.operator, ConditionOperator.BETWEEN)
        self.assertEqual(between_condition.target_value_min, 30.0)
        self.assertEqual(between_condition.target_value_max, 70.0)
        
        # 测试CROSS_ABOVE操作符
        cross_condition = StrategyCondition.objects.create(
            condition_group=condition_group,
            indicator=self.indicator_sma,
            indicator_parameters={'period': 20},
            operator=ConditionOperator.CROSS_ABOVE,
            compare_indicator=self.indicator_sma,
            compare_indicator_parameters={'period': 50}
        )
        
        self.assertEqual(cross_condition.operator, ConditionOperator.CROSS_ABOVE)
        self.assertEqual(cross_condition.compare_indicator, self.indicator_sma)
    
    def test_strategy_string_representation(self):
        """测试模型的字符串表示"""
        strategy = Strategy.objects.create(
            name='测试策略',
            strategy_type=StrategyType.INDICATOR_BASED,
            user=self.user
        )
        
        expected_str = f"测试策略 ({strategy.get_strategy_type_display()})"
        self.assertEqual(str(strategy), expected_str)
        
        indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)
        expected_str = f"指标策略: {strategy.name}"
        self.assertEqual(str(indicator_strategy), expected_str)
    
    def test_strategy_ordering(self):
        """测试策略排序"""
        strategy1 = Strategy.objects.create(
            name='策略1',
            strategy_type=StrategyType.INDICATOR_BASED,
            user=self.user
        )
        
        strategy2 = Strategy.objects.create(
            name='策略2',
            strategy_type=StrategyType.CODE_BASED,
            user=self.user
        )
        
        strategies = Strategy.objects.all()
        # 应该按创建时间倒序排列
        self.assertEqual(strategies.first(), strategy2)
        self.assertEqual(strategies.last(), strategy1)
