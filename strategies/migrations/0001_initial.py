# Generated by Django 5.2 on 2025-06-24 15:04

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("indicators", "0002_alter_indicator_options_indicator_category"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Strategy",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="策略名称")),
                ("description", models.TextField(blank=True, verbose_name="策略描述")),
                (
                    "strategy_type",
                    models.CharField(
                        choices=[("INDICATOR", "基于指标"), ("CODE", "代码策略")],
                        max_length=10,
                        verbose_name="策略类型",
                    ),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否启用"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="strategies",
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="创建用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "策略",
                "verbose_name_plural": "策略",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="IndicatorStrategy",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "strategy",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="indicator_implementation",
                        to="strategies.strategy",
                        verbose_name="关联策略",
                    ),
                ),
            ],
            options={
                "verbose_name": "指标策略",
                "verbose_name_plural": "指标策略",
            },
        ),
        migrations.CreateModel(
            name="CodeStrategy",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("source_code", models.TextField(verbose_name="策略源代码")),
                (
                    "parameters",
                    models.JSONField(
                        default=dict,
                        help_text="格式：{'参数名': {'type': '类型', 'default': '默认值', 'description': '描述'}}",
                        verbose_name="策略参数",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "strategy",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="code_implementation",
                        to="strategies.strategy",
                        verbose_name="关联策略",
                    ),
                ),
            ],
            options={
                "verbose_name": "代码策略",
                "verbose_name_plural": "代码策略",
            },
        ),
        migrations.CreateModel(
            name="StrategyBacktest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="回测名称")),
                (
                    "symbols",
                    models.JSONField(help_text="交易标的列表", verbose_name="交易标的"),
                ),
                ("start_date", models.DateTimeField(verbose_name="开始时间")),
                ("end_date", models.DateTimeField(verbose_name="结束时间")),
                ("timeframe", models.CharField(max_length=20, verbose_name="时间周期")),
                (
                    "initial_cash",
                    models.FloatField(
                        default=100000.0,
                        validators=[django.core.validators.MinValueValidator(1000.0)],
                        verbose_name="初始资金",
                    ),
                ),
                (
                    "commission",
                    models.FloatField(
                        default=0.001,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                        verbose_name="手续费率",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("running", "运行中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "result",
                    models.JSONField(blank=True, null=True, verbose_name="回测结果"),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "strategy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="backtests",
                        to="strategies.strategy",
                        verbose_name="策略",
                    ),
                ),
            ],
            options={
                "verbose_name": "策略回测",
                "verbose_name_plural": "策略回测",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="StrategyConditionGroup",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="条件组名称")),
                (
                    "is_entry",
                    models.BooleanField(
                        help_text="True为开仓条件，False为平仓条件",
                        verbose_name="是否为开仓条件",
                    ),
                ),
                (
                    "logical_operator",
                    models.CharField(
                        choices=[("AND", "且"), ("OR", "或")],
                        default="AND",
                        max_length=5,
                        verbose_name="组内逻辑关系",
                    ),
                ),
                (
                    "order",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="排序",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "indicator_strategy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="condition_groups",
                        to="strategies.indicatorstrategy",
                        verbose_name="指标策略",
                    ),
                ),
            ],
            options={
                "verbose_name": "策略条件组",
                "verbose_name_plural": "策略条件组",
                "ordering": ["order"],
            },
        ),
        migrations.CreateModel(
            name="StrategyCondition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "indicator_parameters",
                    models.JSONField(
                        default=dict,
                        help_text="指标的参数配置",
                        verbose_name="指标参数",
                    ),
                ),
                (
                    "operator",
                    models.CharField(
                        choices=[
                            ("GT", "大于"),
                            ("LT", "小于"),
                            ("EQ", "等于"),
                            ("GTE", "大于等于"),
                            ("LTE", "小于等于"),
                            ("BETWEEN", "介于"),
                            ("CROSS_ABOVE", "向上穿越"),
                            ("CROSS_BELOW", "向下穿越"),
                        ],
                        max_length=15,
                        verbose_name="比较操作符",
                    ),
                ),
                (
                    "target_value",
                    models.FloatField(blank=True, null=True, verbose_name="目标值"),
                ),
                (
                    "target_value_min",
                    models.FloatField(
                        blank=True,
                        help_text="用于BETWEEN操作符",
                        null=True,
                        verbose_name="目标值最小值",
                    ),
                ),
                (
                    "target_value_max",
                    models.FloatField(
                        blank=True,
                        help_text="用于BETWEEN操作符",
                        null=True,
                        verbose_name="目标值最大值",
                    ),
                ),
                (
                    "compare_indicator_parameters",
                    models.JSONField(
                        default=dict,
                        help_text="比较指标的参数配置",
                        verbose_name="比较指标参数",
                    ),
                ),
                (
                    "order",
                    models.IntegerField(
                        default=0,
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="排序",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "compare_indicator",
                    models.ForeignKey(
                        blank=True,
                        help_text="用于指标间比较，如穿越等",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="compare_conditions",
                        to="indicators.indicator",
                        verbose_name="比较指标",
                    ),
                ),
                (
                    "indicator",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="indicators.indicator",
                        verbose_name="指标",
                    ),
                ),
                (
                    "condition_group",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="conditions",
                        to="strategies.strategyconditiongroup",
                        verbose_name="条件组",
                    ),
                ),
            ],
            options={
                "verbose_name": "策略条件",
                "verbose_name_plural": "策略条件",
                "ordering": ["order"],
            },
        ),
    ]
