# Generated by Django 5.2 on 2025-08-05 02:22

import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("strategies", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="StrategyExecutionTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="任务名称")),
                ("description", models.TextField(blank=True, verbose_name="任务描述")),
                (
                    "container_id",
                    models.CharField(
                        blank=True,
                        help_text="Podman容器ID，用作任务标识符",
                        max_length=64,
                        null=True,
                        unique=True,
                        verbose_name="容器ID",
                    ),
                ),
                (
                    "container_name",
                    models.CharField(
                        blank=True, max_length=100, null=True, verbose_name="容器名称"
                    ),
                ),
                (
                    "data_source",
                    models.CharField(
                        choices=[
                            ("influxdb", "InfluxDB"),
                            ("timescaledb", "TimescaleDB"),
                            ("csv", "CSV文件"),
                        ],
                        default="timescaledb",
                        max_length=20,
                        verbose_name="数据源",
                    ),
                ),
                (
                    "symbols",
                    models.JSONField(help_text="交易标的列表", verbose_name="交易标的"),
                ),
                ("start_date", models.DateTimeField(verbose_name="开始时间")),
                ("end_date", models.DateTimeField(verbose_name="结束时间")),
                (
                    "timeframe",
                    models.CharField(
                        default="1d", max_length=20, verbose_name="时间周期"
                    ),
                ),
                (
                    "initial_cash",
                    models.FloatField(
                        default=100000.0,
                        validators=[django.core.validators.MinValueValidator(1000.0)],
                        verbose_name="初始资金",
                    ),
                ),
                (
                    "commission",
                    models.FloatField(
                        default=0.001,
                        validators=[
                            django.core.validators.MinValueValidator(0.0),
                            django.core.validators.MaxValueValidator(1.0),
                        ],
                        verbose_name="手续费率",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待中"),
                            ("running", "运行中"),
                            ("completed", "已完成"),
                            ("failed", "失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="状态",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="开始时间"
                    ),
                ),
                (
                    "finished_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="完成时间"
                    ),
                ),
                (
                    "result",
                    models.JSONField(blank=True, null=True, verbose_name="执行结果"),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                (
                    "logs",
                    models.TextField(blank=True, null=True, verbose_name="执行日志"),
                ),
                (
                    "container_config",
                    models.JSONField(
                        default=dict,
                        help_text="容器运行时配置参数",
                        verbose_name="容器配置",
                    ),
                ),
                (
                    "strategy",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="execution_tasks",
                        to="strategies.strategy",
                        verbose_name="策略",
                    ),
                ),
            ],
            options={
                "verbose_name": "策略执行任务",
                "verbose_name_plural": "策略执行任务",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["container_id"], name="strategies__contain_7ba5fc_idx"
                    ),
                    models.Index(
                        fields=["status"], name="strategies__status_66d288_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="strategies__created_5d6c95_idx"
                    ),
                ],
            },
        ),
    ]
