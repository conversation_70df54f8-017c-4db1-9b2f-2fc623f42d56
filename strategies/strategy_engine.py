"""
策略执行引擎
将指标策略转换为backtrader代码并执行
"""
import backtrader as bt
from typing import Dict, List, Any, Optional
from .models import (
    Strategy, IndicatorStrategy, CodeStrategy, 
    StrategyConditionGroup, StrategyCondition,
    ConditionOperator, LogicalOperator
)
from indicators.models import Indicator


class IndicatorStrategyExecutor(bt.Strategy):
    """基于指标的策略执行器"""
    
    def __init__(self, strategy_config: Dict[str, Any]):
        self.strategy_config = strategy_config
        self.condition_groups = strategy_config.get('condition_groups', [])
        self.indicators = {}
        self.setup_indicators()
    
    def setup_indicators(self):
        """设置策略所需的指标"""
        for group in self.condition_groups:
            for condition in group.get('conditions', []):
                indicator_id = condition['indicator_id']
                indicator_class = condition['indicator_class']
                parameters = condition.get('indicator_parameters', {})
                
                # 避免重复创建相同的指标
                indicator_key = f"{indicator_id}_{hash(str(parameters))}"
                if indicator_key not in self.indicators:
                    # 动态创建指标实例
                    indicator_instance = self.create_indicator(indicator_class, parameters)
                    self.indicators[indicator_key] = indicator_instance
                    
                # 为比较指标也创建实例
                if condition.get('compare_indicator_id'):
                    compare_indicator_id = condition['compare_indicator_id']
                    compare_indicator_class = condition['compare_indicator_class']
                    compare_parameters = condition.get('compare_indicator_parameters', {})
                    
                    compare_key = f"{compare_indicator_id}_{hash(str(compare_parameters))}"
                    if compare_key not in self.indicators:
                        compare_indicator_instance = self.create_indicator(
                            compare_indicator_class, compare_parameters
                        )
                        self.indicators[compare_key] = compare_indicator_instance
    
    def create_indicator(self, indicator_class: str, parameters: Dict[str, Any]):
        """创建指标实例"""
        try:
            # 尝试从backtrader.indicators获取指标类
            if hasattr(bt.indicators, indicator_class):
                indicator_cls = getattr(bt.indicators, indicator_class)
                return indicator_cls(self.data, **parameters)
            else:
                # 如果不存在，返回简单移动平均线作为默认
                return bt.indicators.SimpleMovingAverage(self.data, period=20)
        except Exception as e:
            print(f"创建指标失败: {indicator_class}, 错误: {e}")
            return bt.indicators.SimpleMovingAverage(self.data, period=20)
    
    def next(self):
        """策略主逻辑"""
        # 检查开仓条件
        if not self.position:
            if self.check_entry_conditions():
                self.buy()
        
        # 检查平仓条件
        elif self.position:
            if self.check_exit_conditions():
                self.sell()
    
    def check_entry_conditions(self) -> bool:
        """检查开仓条件"""
        entry_groups = [group for group in self.condition_groups if group['is_entry']]
        
        if not entry_groups:
            return False
        
        # 所有开仓条件组都需要满足（组间为AND关系）
        for group in entry_groups:
            if not self.evaluate_condition_group(group):
                return False
        
        return True
    
    def check_exit_conditions(self) -> bool:
        """检查平仓条件"""
        exit_groups = [group for group in self.condition_groups if not group['is_entry']]
        
        if not exit_groups:
            return False
        
        # 任一平仓条件组满足即可平仓（组间为OR关系）
        for group in exit_groups:
            if self.evaluate_condition_group(group):
                return True
        
        return False
    
    def evaluate_condition_group(self, group: Dict[str, Any]) -> bool:
        """评估条件组"""
        conditions = group.get('conditions', [])
        if not conditions:
            return False
        
        logical_operator = group.get('logical_operator', 'AND')
        results = []
        
        for condition in conditions:
            result = self.evaluate_condition(condition)
            results.append(result)
        
        # 根据逻辑操作符组合结果
        if logical_operator == 'AND':
            return all(results)
        elif logical_operator == 'OR':
            return any(results)
        else:
            return False
    
    def evaluate_condition(self, condition: Dict[str, Any]) -> bool:
        """评估单个条件"""
        indicator_id = condition['indicator_id']
        indicator_key = f"{indicator_id}_{hash(str(condition.get('indicator_parameters', {})))}"
        
        if indicator_key not in self.indicators:
            return False
        
        indicator = self.indicators[indicator_key]
        operator = condition['operator']
        
        # 获取指标当前值
        try:
            current_value = indicator[0]
        except (IndexError, TypeError):
            return False
        
        # 根据操作符进行比较
        if operator == 'GT':
            target_value = condition.get('target_value')
            return current_value > target_value if target_value is not None else False
        
        elif operator == 'LT':
            target_value = condition.get('target_value')
            return current_value < target_value if target_value is not None else False
        
        elif operator == 'EQ':
            target_value = condition.get('target_value')
            return abs(current_value - target_value) < 1e-8 if target_value is not None else False
        
        elif operator == 'GTE':
            target_value = condition.get('target_value')
            return current_value >= target_value if target_value is not None else False
        
        elif operator == 'LTE':
            target_value = condition.get('target_value')
            return current_value <= target_value if target_value is not None else False
        
        elif operator == 'BETWEEN':
            min_value = condition.get('target_value_min')
            max_value = condition.get('target_value_max')
            if min_value is not None and max_value is not None:
                return min_value <= current_value <= max_value
            return False
        
        elif operator == 'CROSS_ABOVE':
            compare_indicator_id = condition.get('compare_indicator_id')
            if compare_indicator_id:
                compare_key = f"{compare_indicator_id}_{hash(str(condition.get('compare_indicator_parameters', {})))}"
                if compare_key in self.indicators:
                    compare_indicator = self.indicators[compare_key]
                    try:
                        # 检查是否向上穿越
                        return (current_value > compare_indicator[0] and 
                                indicator[-1] <= compare_indicator[-1])
                    except (IndexError, TypeError):
                        return False
            return False
        
        elif operator == 'CROSS_BELOW':
            compare_indicator_id = condition.get('compare_indicator_id')
            if compare_indicator_id:
                compare_key = f"{compare_indicator_id}_{hash(str(condition.get('compare_indicator_parameters', {})))}"
                if compare_key in self.indicators:
                    compare_indicator = self.indicators[compare_key]
                    try:
                        # 检查是否向下穿越
                        return (current_value < compare_indicator[0] and 
                                indicator[-1] >= compare_indicator[-1])
                    except (IndexError, TypeError):
                        return False
            return False
        
        return False


class StrategyEngine:
    """策略执行引擎"""
    
    @staticmethod
    def create_strategy_from_config(strategy_id: int) -> Optional[bt.Strategy]:
        """根据策略配置创建策略实例"""
        try:
            strategy = Strategy.objects.get(id=strategy_id)
            
            if strategy.strategy_type == 'INDICATOR':
                return StrategyEngine.create_indicator_strategy(strategy)
            elif strategy.strategy_type == 'CODE':
                return StrategyEngine.create_code_strategy(strategy)
            
        except Strategy.DoesNotExist:
            print(f"策略 {strategy_id} 不存在")
            return None
        except Exception as e:
            print(f"创建策略失败: {e}")
            return None
    
    @staticmethod
    def create_indicator_strategy(strategy: Strategy) -> Optional[bt.Strategy]:
        """创建基于指标的策略"""
        try:
            indicator_strategy = strategy.indicator_implementation
            
            # 构建策略配置
            condition_groups = []
            for group in indicator_strategy.condition_groups.all():
                conditions = []
                for condition in group.conditions.all():
                    condition_data = {
                        'indicator_id': condition.indicator.id,
                        'indicator_class': condition.indicator.class_name,
                        'indicator_parameters': condition.indicator_parameters,
                        'operator': condition.operator,
                        'target_value': condition.target_value,
                        'target_value_min': condition.target_value_min,
                        'target_value_max': condition.target_value_max,
                        'compare_indicator_id': condition.compare_indicator.id if condition.compare_indicator else None,
                        'compare_indicator_class': condition.compare_indicator.class_name if condition.compare_indicator else None,
                        'compare_indicator_parameters': condition.compare_indicator_parameters,
                        'order': condition.order
                    }
                    conditions.append(condition_data)
                
                group_data = {
                    'name': group.name,
                    'is_entry': group.is_entry,
                    'logical_operator': group.logical_operator,
                    'conditions': conditions,
                    'order': group.order
                }
                condition_groups.append(group_data)
            
            strategy_config = {
                'condition_groups': condition_groups
            }
            
            # 创建策略类
            class CustomIndicatorStrategy(IndicatorStrategyExecutor):
                def __init__(self):
                    super().__init__(strategy_config)
            
            return CustomIndicatorStrategy
            
        except Exception as e:
            print(f"创建指标策略失败: {e}")
            return None
    
    @staticmethod
    def create_code_strategy(strategy: Strategy) -> Optional[bt.Strategy]:
        """创建代码策略"""
        try:
            code_strategy = strategy.code_implementation
            
            # 动态执行用户代码
            namespace = {
                'bt': bt,
                'backtrader': bt,
            }
            
            exec(code_strategy.source_code, namespace)
            
            # 查找策略类（假设用户代码中定义了名为 'Strategy' 的类）
            if 'Strategy' in namespace:
                return namespace['Strategy']
            else:
                print("用户代码中未找到 'Strategy' 类")
                return None
                
        except Exception as e:
            print(f"创建代码策略失败: {e}")
            return None
    
    @staticmethod
    def run_backtest(strategy_id: int, backtest_config: Dict[str, Any]) -> Dict[str, Any]:
        """运行回测"""
        try:
            cerebro = bt.Cerebro()
            
            # 创建策略
            strategy_class = StrategyEngine.create_strategy_from_config(strategy_id)
            if not strategy_class:
                return {"error": "策略创建失败"}
            
            cerebro.addstrategy(strategy_class)
            
            # 设置初始资金
            cerebro.broker.setcash(backtest_config.get('initial_cash', 100000.0))
            
            # 设置手续费
            cerebro.broker.setcommission(commission=backtest_config.get('commission', 0.001))
            
            # 添加数据源（这里需要根据实际情况实现）
            # cerebro.adddata(data_feed)
            
            # 运行回测
            results = cerebro.run()
            
            # 获取最终资产
            final_value = cerebro.broker.getvalue()
            
            return {
                "success": True,
                "initial_cash": backtest_config.get('initial_cash', 100000.0),
                "final_value": final_value,
                "profit": final_value - backtest_config.get('initial_cash', 100000.0),
                "return_rate": (final_value / backtest_config.get('initial_cash', 100000.0) - 1) * 100
            }
            
        except Exception as e:
            return {"error": str(e)} 