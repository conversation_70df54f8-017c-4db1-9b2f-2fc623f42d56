from ninja_extra import api_controller, route
from ninja_jwt.authentication import <PERSON><PERSON><PERSON><PERSON>
from typing import List, Optional
from django.db.models import Q, Prefetch
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from pydantic import BaseModel
from .models import (
    Strategy, CodeStrategy, IndicatorStrategy,
    StrategyConditionGroup, StrategyCondition, StrategyBacktest,
    StrategyExecutionTask, StrategyType, ConditionOperator, LogicalOperator
)
from indicators.models import Indicator

User = get_user_model()


# 权限检查装饰器
def check_strategy_permission(func):
    """检查策略权限的装饰器"""
    def wrapper(self, request, strategy_id: int, *args, **kwargs):
        try:
            strategy = Strategy.objects.get(id=strategy_id)
            if strategy.user != request.user and not request.user.is_superuser:
                return {"error": "无权限访问此策略", "code": 403}
        except Strategy.DoesNotExist:
            return {"error": "策略不存在", "code": 404}

        return func(self, request, strategy_id, *args, **kwargs)
    return wrapper


def check_task_permission(func):
    """检查执行任务权限的装饰器"""
    def wrapper(self, request, task_id: int, *args, **kwargs):
        try:
            task = StrategyExecutionTask.objects.select_related('strategy').get(id=task_id)
            if task.strategy.user != request.user and not request.user.is_superuser:
                return {"error": "无权限访问此任务", "code": 403}
        except StrategyExecutionTask.DoesNotExist:
            return {"error": "任务不存在", "code": 404}

        return func(self, request, task_id, *args, **kwargs)
    return wrapper


# Pydantic schemas for request/response
class StrategyConditionSchema(BaseModel):
    indicator_id: int
    indicator_parameters: dict = {}
    operator: str
    target_value: Optional[float] = None
    target_value_min: Optional[float] = None
    target_value_max: Optional[float] = None
    compare_indicator_id: Optional[int] = None
    compare_indicator_parameters: dict = {}
    order: int = 0


class StrategyConditionGroupSchema(BaseModel):
    name: str
    is_entry: bool
    logical_operator: str = "AND"
    conditions: List[StrategyConditionSchema]
    order: int = 0


class CreateIndicatorStrategySchema(BaseModel):
    name: str
    description: str = ""
    condition_groups: List[StrategyConditionGroupSchema]


class CreateCodeStrategySchema(BaseModel):
    name: str
    description: str = ""
    source_code: str
    parameters: dict = {}


class CreateBacktestSchema(BaseModel):
    strategy_id: int
    name: str
    symbols: List[str]
    start_date: str
    end_date: str
    timeframe: str
    initial_cash: float = 100000.0
    commission: float = 0.001


class CreateExecutionTaskSchema(BaseModel):
    strategy_id: int
    name: str
    description: str = ""
    data_source: str = "timescaledb"
    symbols: List[str]
    start_date: str
    end_date: str
    timeframe: str = "1d"
    initial_cash: float = 100000.0
    commission: float = 0.001
    container_config: dict = {}


@api_controller("/strategies", tags=["策略管理"], auth=JWTAuth())
class StrategyController:
    
    @route.get("/strategies", response=List[dict])
    def list_strategies(
        self, 
        request,
        strategy_type: Optional[str] = None,
        search: Optional[str] = None,
        user_id: Optional[int] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0
    ):
        """获取策略列表"""
        queryset = Strategy.objects.select_related('user').prefetch_related(
            'code_implementation',
            'indicator_implementation__condition_groups__conditions__indicator'
        )
        
        # 按类型筛选
        if strategy_type:
            queryset = queryset.filter(strategy_type=strategy_type)
        
        # 按用户筛选
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        
        # 搜索功能
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )
        
        # 排序
        queryset = queryset.order_by('-created_at')
        
        # 分页
        if limit:
            queryset = queryset[offset:offset + limit]
        
        result = []
        for strategy in queryset:
            strategy_data = {
                "id": strategy.id,
                "name": strategy.name,
                "description": strategy.description,
                "strategy_type": strategy.strategy_type,
                "strategy_type_display": strategy.get_strategy_type_display(),
                "user": {
                    "id": strategy.user.id,
                    "username": strategy.user.username
                },
                "is_active": strategy.is_active,
                "created_at": strategy.created_at,
                "updated_at": strategy.updated_at
            }
            
            # 添加策略实现详情
            if strategy.strategy_type == StrategyType.CODE_BASED:
                if hasattr(strategy, 'code_implementation'):
                    strategy_data["code_implementation"] = {
                        "source_code": strategy.code_implementation.source_code,
                        "parameters": strategy.code_implementation.parameters
                    }
            elif strategy.strategy_type == StrategyType.INDICATOR_BASED:
                if hasattr(strategy, 'indicator_implementation'):
                    condition_groups = []
                    for group in strategy.indicator_implementation.condition_groups.all():
                        conditions = []
                        for condition in group.conditions.all():
                            conditions.append({
                                "id": condition.id,
                                "indicator": {
                                    "id": condition.indicator.id,
                                    "display_name": condition.indicator.display_name
                                },
                                "indicator_parameters": condition.indicator_parameters,
                                "operator": condition.operator,
                                "operator_display": condition.get_operator_display(),
                                "target_value": condition.target_value,
                                "target_value_min": condition.target_value_min,
                                "target_value_max": condition.target_value_max,
                                "compare_indicator": {
                                    "id": condition.compare_indicator.id,
                                    "display_name": condition.compare_indicator.display_name
                                } if condition.compare_indicator else None,
                                "compare_indicator_parameters": condition.compare_indicator_parameters,
                                "order": condition.order
                            })
                        
                        condition_groups.append({
                            "id": group.id,
                            "name": group.name,
                            "is_entry": group.is_entry,
                            "logical_operator": group.logical_operator,
                            "logical_operator_display": group.get_logical_operator_display(),
                            "conditions": conditions,
                            "order": group.order
                        })
                    
                    strategy_data["indicator_implementation"] = {
                        "condition_groups": condition_groups
                    }
            
            result.append(strategy_data)
        
        return result
    
    @route.get("/strategies/{strategy_id}", response=dict)
    @check_strategy_permission
    def get_strategy(self, request, strategy_id: int):
        """获取单个策略详情"""
        try:
            strategy = Strategy.objects.select_related('user').prefetch_related(
                'code_implementation',
                'indicator_implementation__condition_groups__conditions__indicator',
                'indicator_implementation__condition_groups__conditions__compare_indicator'
            ).get(id=strategy_id)
            
            strategy_data = {
                "id": strategy.id,
                "name": strategy.name,
                "description": strategy.description,
                "strategy_type": strategy.strategy_type,
                "strategy_type_display": strategy.get_strategy_type_display(),
                "user": {
                    "id": strategy.user.id,
                    "username": strategy.user.username
                },
                "is_active": strategy.is_active,
                "created_at": strategy.created_at,
                "updated_at": strategy.updated_at
            }
            
            # 添加策略实现详情
            if strategy.strategy_type == StrategyType.CODE_BASED:
                if hasattr(strategy, 'code_implementation'):
                    strategy_data["code_implementation"] = {
                        "source_code": strategy.code_implementation.source_code,
                        "parameters": strategy.code_implementation.parameters
                    }
            elif strategy.strategy_type == StrategyType.INDICATOR_BASED:
                if hasattr(strategy, 'indicator_implementation'):
                    condition_groups = []
                    for group in strategy.indicator_implementation.condition_groups.all():
                        conditions = []
                        for condition in group.conditions.all():
                            conditions.append({
                                "id": condition.id,
                                "indicator": {
                                    "id": condition.indicator.id,
                                    "display_name": condition.indicator.display_name,
                                    "class_name": condition.indicator.class_name
                                },
                                "indicator_parameters": condition.indicator_parameters,
                                "operator": condition.operator,
                                "operator_display": condition.get_operator_display(),
                                "target_value": condition.target_value,
                                "target_value_min": condition.target_value_min,
                                "target_value_max": condition.target_value_max,
                                "compare_indicator": {
                                    "id": condition.compare_indicator.id,
                                    "display_name": condition.compare_indicator.display_name,
                                    "class_name": condition.compare_indicator.class_name
                                } if condition.compare_indicator else None,
                                "compare_indicator_parameters": condition.compare_indicator_parameters,
                                "order": condition.order
                            })
                        
                        condition_groups.append({
                            "id": group.id,
                            "name": group.name,
                            "is_entry": group.is_entry,
                            "logical_operator": group.logical_operator,
                            "logical_operator_display": group.get_logical_operator_display(),
                            "conditions": conditions,
                            "order": group.order
                        })
                    
                    strategy_data["indicator_implementation"] = {
                        "condition_groups": condition_groups
                    }
            
            return strategy_data
            
        except Strategy.DoesNotExist:
            return {"error": "策略不存在"}
    
    @route.post("/strategies/indicator", response=dict)
    def create_indicator_strategy(self, request, payload: CreateIndicatorStrategySchema):
        """创建基于指标的策略"""
        try:
            # 创建策略基础记录
            strategy = Strategy.objects.create(
                name=payload.name,
                description=payload.description,
                strategy_type=StrategyType.INDICATOR_BASED,
                user_id=request.user.id if request.user.is_authenticated else 1  # 默认用户
            )
            
            # 创建指标策略实现
            indicator_strategy = IndicatorStrategy.objects.create(strategy=strategy)
            
            # 创建条件组和条件
            for group_data in payload.condition_groups:
                condition_group = StrategyConditionGroup.objects.create(
                    indicator_strategy=indicator_strategy,
                    name=group_data.name,
                    is_entry=group_data.is_entry,
                    logical_operator=group_data.logical_operator,
                    order=group_data.order
                )
                
                for condition_data in group_data.conditions:
                    StrategyCondition.objects.create(
                        condition_group=condition_group,
                        indicator_id=condition_data.indicator_id,
                        indicator_parameters=condition_data.indicator_parameters,
                        operator=condition_data.operator,
                        target_value=condition_data.target_value,
                        target_value_min=condition_data.target_value_min,
                        target_value_max=condition_data.target_value_max,
                        compare_indicator_id=condition_data.compare_indicator_id,
                        compare_indicator_parameters=condition_data.compare_indicator_parameters,
                        order=condition_data.order
                    )
            
            return {"success": True, "strategy_id": strategy.id, "message": "指标策略创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.post("/strategies/code", response=dict)
    def create_code_strategy(self, request, payload: CreateCodeStrategySchema):
        """创建代码策略"""
        try:
            # 创建策略基础记录
            strategy = Strategy.objects.create(
                name=payload.name,
                description=payload.description,
                strategy_type=StrategyType.CODE_BASED,
                user_id=request.user.id if request.user.is_authenticated else 1  # 默认用户
            )
            
            # 创建代码策略实现
            CodeStrategy.objects.create(
                strategy=strategy,
                source_code=payload.source_code,
                parameters=payload.parameters
            )
            
            return {"success": True, "strategy_id": strategy.id, "message": "代码策略创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.put("/strategies/{strategy_id}", response=dict)
    @check_strategy_permission
    def update_strategy(self, request, strategy_id: int, payload: dict):
        """更新策略"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            
            # 更新基础信息
            strategy.name = payload.get('name', strategy.name)
            strategy.description = payload.get('description', strategy.description)
            strategy.is_active = payload.get('is_active', strategy.is_active)
            strategy.save()
            
            return {"success": True, "message": "策略更新成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.delete("/strategies/{strategy_id}", response=dict)
    @check_strategy_permission
    def delete_strategy(self, request, strategy_id: int):
        """删除策略"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            strategy.delete()
            return {"success": True, "message": "策略删除成功"}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.get("/enums", response=dict)
    def get_enums(self, request):
        """获取枚举值"""
        return {
            "strategy_types": [
                {"value": choice.value, "label": choice.label}
                for choice in StrategyType
            ],
            "condition_operators": [
                {"value": choice.value, "label": choice.label}
                for choice in ConditionOperator
            ],
            "logical_operators": [
                {"value": choice.value, "label": choice.label}
                for choice in LogicalOperator
            ]
        }
    
    @route.post("/strategies/{strategy_id}/backtest", response=dict)
    @check_strategy_permission
    def create_backtest(self, request, strategy_id: int, payload: CreateBacktestSchema):
        """创建策略回测"""
        try:
            strategy = get_object_or_404(Strategy, id=strategy_id)
            
            backtest = StrategyBacktest.objects.create(
                strategy=strategy,
                name=payload.name,
                symbols=payload.symbols,
                start_date=payload.start_date,
                end_date=payload.end_date,
                timeframe=payload.timeframe,
                initial_cash=payload.initial_cash,
                commission=payload.commission
            )
            
            return {"success": True, "backtest_id": backtest.id, "message": "回测任务创建成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.get("/strategies/{strategy_id}/backtests", response=List[dict])
    @check_strategy_permission
    def get_strategy_backtests(self, request, strategy_id: int):
        """获取策略的回测记录"""
        backtests = StrategyBacktest.objects.filter(strategy_id=strategy_id).order_by('-created_at')
        
        return [
            {
                "id": bt.id,
                "name": bt.name,
                "symbols": bt.symbols,
                "start_date": bt.start_date,
                "end_date": bt.end_date,
                "timeframe": bt.timeframe,
                "initial_cash": bt.initial_cash,
                "commission": bt.commission,
                "status": bt.status,
                "result": bt.result,
                "error_message": bt.error_message,
                "created_at": bt.created_at,
                "updated_at": bt.updated_at
            }
            for bt in backtests
        ]

    # ==================== 容器化策略执行任务管理 ====================

    @route.post("/execution-tasks", response=dict)
    def create_execution_task(self, request, payload: CreateExecutionTaskSchema):
        """创建策略执行任务（容器化）"""
        try:
            from .container_orchestrator import ContainerOrchestrator, ContainerExecutionError
            from datetime import datetime

            # 验证策略存在并检查权限
            strategy = get_object_or_404(Strategy, id=payload.strategy_id)

            # 检查用户权限
            if strategy.user != request.user and not request.user.is_superuser:
                return {"success": False, "error": "无权限访问此策略", "code": 403}

            if not strategy.is_active:
                return {"success": False, "error": "策略未激活"}

            # 解析日期
            try:
                start_date = datetime.strptime(payload.start_date, '%Y-%m-%d').date()
                end_date = datetime.strptime(payload.end_date, '%Y-%m-%d').date()
            except ValueError:
                return {"success": False, "error": "日期格式错误，请使用 YYYY-MM-DD 格式"}

            if start_date >= end_date:
                return {"success": False, "error": "开始日期必须早于结束日期"}

            # 创建执行任务记录
            task = StrategyExecutionTask.objects.create(
                strategy=strategy,
                name=payload.name,
                description=payload.description,
                data_source=payload.data_source,
                symbols=payload.symbols,
                start_date=start_date,
                end_date=end_date,
                timeframe=payload.timeframe,
                initial_cash=payload.initial_cash,
                commission=payload.commission,
                container_config=payload.container_config,
                status='pending'
            )

            # 启动容器执行策略
            orchestrator = ContainerOrchestrator()

            try:
                container_id = orchestrator.create_and_run_strategy(
                    strategy_id=payload.strategy_id,
                    data_source=payload.data_source,
                    symbols=payload.symbols,
                    start_date=payload.start_date,
                    end_date=payload.end_date,
                    timeframe=payload.timeframe,
                    initial_cash=payload.initial_cash,
                    commission=payload.commission,
                    **payload.container_config
                )

                # 更新任务状态
                task.mark_as_running(container_id, f"strategy-{payload.strategy_id}-{container_id[:8]}")

                return {
                    "success": True,
                    "message": "策略执行任务已创建并启动",
                    "task_id": task.id,
                    "container_id": container_id
                }

            except ContainerExecutionError as e:
                # 容器启动失败，标记任务为失败
                task.mark_as_failed(str(e))
                return {"success": False, "error": f"容器启动失败: {str(e)}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    @route.get("/execution-tasks", response=List[dict])
    def list_execution_tasks(
        self,
        request,
        strategy_id: Optional[int] = None,
        status: Optional[str] = None,
        limit: Optional[int] = None,
        offset: Optional[int] = 0
    ):
        """获取策略执行任务列表 - 只返回当前用户的任务"""
        # 基础查询：只返回当前用户的任务
        if request.user.is_superuser:
            queryset = StrategyExecutionTask.objects.select_related('strategy')
        else:
            queryset = StrategyExecutionTask.objects.select_related('strategy').filter(strategy__user=request.user)

        # 按策略筛选
        if strategy_id:
            # 检查策略权限
            try:
                strategy = Strategy.objects.get(id=strategy_id)
                if strategy.user != request.user and not request.user.is_superuser:
                    return {"error": "无权限访问此策略的任务", "code": 403}
                queryset = queryset.filter(strategy_id=strategy_id)
            except Strategy.DoesNotExist:
                return {"error": "策略不存在", "code": 404}

        # 按状态筛选
        if status:
            queryset = queryset.filter(status=status)

        # 排序
        queryset = queryset.order_by('-created_at')

        # 分页
        if limit:
            queryset = queryset[offset:offset + limit]

        result = []
        for task in queryset:
            task_data = {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "strategy": {
                    "id": task.strategy.id,
                    "name": task.strategy.name
                },
                "container_id": task.container_id,
                "container_name": task.container_name,
                "data_source": task.data_source,
                "symbols": task.symbols,
                "start_date": task.start_date,
                "end_date": task.end_date,
                "timeframe": task.timeframe,
                "initial_cash": task.initial_cash,
                "commission": task.commission,
                "status": task.status,
                "status_display": task.get_status_display(),
                "created_at": task.created_at,
                "started_at": task.started_at,
                "finished_at": task.finished_at,
                "duration": str(task.duration) if task.duration else None,
                "result": task.result,
                "error_message": task.error_message
            }
            result.append(task_data)

        return result

    @route.get("/execution-tasks/{task_id}", response=dict)
    @check_task_permission
    def get_execution_task(self, request, task_id: int):
        """获取单个执行任务详情"""
        try:
            task = StrategyExecutionTask.objects.select_related('strategy').get(id=task_id)

            return {
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "strategy": {
                    "id": task.strategy.id,
                    "name": task.strategy.name,
                    "strategy_type": task.strategy.strategy_type
                },
                "container_id": task.container_id,
                "container_name": task.container_name,
                "data_source": task.data_source,
                "symbols": task.symbols,
                "start_date": task.start_date,
                "end_date": task.end_date,
                "timeframe": task.timeframe,
                "initial_cash": task.initial_cash,
                "commission": task.commission,
                "status": task.status,
                "status_display": task.get_status_display(),
                "created_at": task.created_at,
                "started_at": task.started_at,
                "finished_at": task.finished_at,
                "duration": str(task.duration) if task.duration else None,
                "result": task.result,
                "error_message": task.error_message,
                "logs": task.logs,
                "container_config": task.container_config
            }

        except StrategyExecutionTask.DoesNotExist:
            return {"error": "执行任务不存在"}

    @route.get("/execution-tasks/{task_id}/status", response=dict)
    @check_task_permission
    def get_task_status(self, request, task_id: int):
        """获取任务状态（实时从容器查询）"""
        try:
            from .container_orchestrator import ContainerOrchestrator, ContainerNotFoundError

            task = get_object_or_404(StrategyExecutionTask, id=task_id)

            if not task.container_id:
                return {
                    "task_id": task_id,
                    "status": task.status,
                    "message": "任务尚未启动容器"
                }

            orchestrator = ContainerOrchestrator()

            try:
                # 从容器获取实时状态
                container_status = orchestrator.get_container_status(task.container_id)

                # 根据容器状态更新任务状态
                if container_status['running']:
                    if task.status != 'running':
                        task.mark_as_running(task.container_id, task.container_name)
                elif container_status['status'] == 'exited':
                    if task.status == 'running':
                        if container_status['exit_code'] == 0:
                            # 尝试获取结果
                            logs = orchestrator.get_container_logs(task.container_id)
                            task.logs = logs
                            # 这里可以解析日志中的JSON结果
                            task.mark_as_completed()
                        else:
                            error_msg = container_status.get('error', f"容器退出码: {container_status['exit_code']}")
                            task.mark_as_failed(error_msg)

                return {
                    "task_id": task_id,
                    "status": task.status,
                    "container_status": container_status,
                    "updated_at": task.updated_at if hasattr(task, 'updated_at') else None
                }

            except ContainerNotFoundError:
                # 容器不存在，可能已被清理
                if task.status == 'running':
                    task.mark_as_failed("容器已不存在")

                return {
                    "task_id": task_id,
                    "status": task.status,
                    "message": "容器不存在"
                }

        except Exception as e:
            return {"error": str(e)}

    @route.post("/execution-tasks/{task_id}/cancel", response=dict)
    @check_task_permission
    def cancel_task(self, request, task_id: int):
        """取消执行任务"""
        try:
            from .container_orchestrator import ContainerOrchestrator, ContainerNotFoundError

            task = get_object_or_404(StrategyExecutionTask, id=task_id)

            if task.is_finished:
                return {"success": False, "error": "任务已完成，无法取消"}

            if not task.container_id:
                # 任务还未启动容器，直接标记为取消
                task.mark_as_cancelled()
                return {"success": True, "message": "任务已取消"}

            orchestrator = ContainerOrchestrator()

            try:
                # 停止容器
                success = orchestrator.stop_container(task.container_id)

                if success:
                    task.mark_as_cancelled()
                    return {"success": True, "message": "任务已取消，容器已停止"}
                else:
                    return {"success": False, "error": "停止容器失败"}

            except ContainerNotFoundError:
                # 容器不存在，直接标记为取消
                task.mark_as_cancelled()
                return {"success": True, "message": "任务已取消（容器不存在）"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    @route.get("/execution-tasks/{task_id}/logs", response=dict)
    @check_task_permission
    def get_task_logs(self, request, task_id: int, tail: Optional[int] = 100):
        """获取任务执行日志"""
        try:
            from .container_orchestrator import ContainerOrchestrator, ContainerNotFoundError

            task = get_object_or_404(StrategyExecutionTask, id=task_id)

            # 如果任务已完成且有保存的日志，直接返回
            if task.is_finished and task.logs:
                return {
                    "task_id": task_id,
                    "logs": task.logs,
                    "source": "database"
                }

            # 如果有容器ID，尝试从容器获取实时日志
            if task.container_id:
                orchestrator = ContainerOrchestrator()

                try:
                    logs = orchestrator.get_container_logs(task.container_id, tail=tail)

                    # 如果任务已完成，保存日志到数据库
                    if task.is_finished:
                        task.logs = logs
                        task.save(update_fields=['logs'])

                    return {
                        "task_id": task_id,
                        "logs": logs,
                        "source": "container"
                    }

                except ContainerNotFoundError:
                    return {
                        "task_id": task_id,
                        "logs": task.logs or "",
                        "source": "database",
                        "message": "容器不存在，返回数据库中的日志"
                    }

            return {
                "task_id": task_id,
                "logs": task.logs or "",
                "source": "database"
            }

        except Exception as e:
            return {"error": str(e)}

    @route.get("/execution-tasks/stats", response=dict)
    def get_execution_stats(self, request):
        """获取执行任务统计信息"""
        try:
            from .container_monitor import get_monitor

            monitor = get_monitor()
            stats = monitor.get_monitoring_stats()

            return {
                "success": True,
                "stats": stats
            }

        except Exception as e:
            return {"success": False, "error": str(e)}