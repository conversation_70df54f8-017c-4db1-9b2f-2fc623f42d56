from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = '创建测试用户'

    def handle(self, *args, **options):
        # 创建测试用户
        test_users = [
            {
                'username': 'testuser1',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': '测试',
                'last_name': '用户1'
            },
            {
                'username': 'testuser2',
                'email': '<EMAIL>',
                'password': 'testpass123',
                'first_name': '测试',
                'last_name': '用户2'
            },
            {
                'username': 'admin',
                'email': '<EMAIL>',
                'password': 'admin123',
                'first_name': '管理',
                'last_name': '员',
                'is_superuser': True,
                'is_staff': True
            }
        ]

        for user_data in test_users:
            username = user_data['username']
            if User.objects.filter(username=username).exists():
                self.stdout.write(
                    self.style.WARNING(f'用户 {username} 已存在，跳过创建')
                )
                continue

            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name']
            )

            if user_data.get('is_superuser'):
                user.is_superuser = True
                user.is_staff = True
                user.save()

            self.stdout.write(
                self.style.SUCCESS(f'成功创建用户: {username}')
            )

        self.stdout.write(
            self.style.SUCCESS('测试用户创建完成！')
        )
        self.stdout.write('用户列表:')
        self.stdout.write('- testuser1 (密码: testpass123)')
        self.stdout.write('- testuser2 (密码: testpass123)')
        self.stdout.write('- admin (密码: admin123, 超级用户)')
