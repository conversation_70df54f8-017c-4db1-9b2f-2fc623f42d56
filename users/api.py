from ninja_extra import api_controller, route
from ninja_jwt.controller import NinjaJ<PERSON><PERSON><PERSON>efaultController
from ninja_jwt.authentication import <PERSON><PERSON><PERSON><PERSON>
from ninja import Schema
from typing import Optional
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.hashers import make_password
from pydantic import BaseModel, EmailStr

User = get_user_model()


# 用户相关的 Schema
class UserRegistrationSchema(BaseModel):
    username: str
    email: EmailStr
    password: str
    first_name: Optional[str] = ""
    last_name: Optional[str] = ""


class UserLoginSchema(BaseModel):
    username: str
    password: str


class UserProfileSchema(Schema):
    id: int
    username: str
    email: str
    first_name: str
    last_name: str
    is_active: bool
    date_joined: str


class UserUpdateSchema(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[EmailStr] = None


@api_controller("/auth", tags=["用户认证"])
class AuthController(NinjaJWTDefaultController):
    """用户认证控制器，继承自 NinjaJWTDefaultController 以获得默认的 JWT 端点"""
    
    @route.post("/register", response=dict)
    def register(self, request, payload: UserRegistrationSchema):
        """用户注册"""
        try:
            # 检查用户名是否已存在
            if User.objects.filter(username=payload.username).exists():
                return {"success": False, "error": "用户名已存在"}
            
            # 检查邮箱是否已存在
            if User.objects.filter(email=payload.email).exists():
                return {"success": False, "error": "邮箱已被注册"}
            
            # 创建用户
            user = User.objects.create(
                username=payload.username,
                email=payload.email,
                password=make_password(payload.password),
                first_name=payload.first_name,
                last_name=payload.last_name
            )
            
            return {
                "success": True,
                "message": "注册成功",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}


@api_controller("/users", tags=["用户管理"], auth=JWTAuth())
class UserController:
    """用户管理控制器"""
    
    @route.get("/profile", response=UserProfileSchema)
    def get_profile(self, request):
        """获取当前用户信息"""
        user = request.user
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "is_active": user.is_active,
            "date_joined": user.date_joined.isoformat()
        }
    
    @route.put("/profile", response=dict)
    def update_profile(self, request, payload: UserUpdateSchema):
        """更新用户信息"""
        try:
            user = request.user
            
            if payload.first_name is not None:
                user.first_name = payload.first_name
            if payload.last_name is not None:
                user.last_name = payload.last_name
            if payload.email is not None:
                # 检查邮箱是否已被其他用户使用
                if User.objects.filter(email=payload.email).exclude(id=user.id).exists():
                    return {"success": False, "error": "邮箱已被其他用户使用"}
                user.email = payload.email
            
            user.save()
            
            return {
                "success": True,
                "message": "用户信息更新成功",
                "user": {
                    "id": user.id,
                    "username": user.username,
                    "email": user.email,
                    "first_name": user.first_name,
                    "last_name": user.last_name
                }
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @route.post("/change-password", response=dict)
    def change_password(self, request, old_password: str, new_password: str):
        """修改密码"""
        try:
            user = request.user
            
            # 验证旧密码
            if not user.check_password(old_password):
                return {"success": False, "error": "旧密码不正确"}
            
            # 设置新密码
            user.set_password(new_password)
            user.save()
            
            return {"success": True, "message": "密码修改成功"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
