# Generated by Django 5.2 on 2025-07-13 02:07

import django.core.validators
import django.db.models.deletion
import timescale.db.models.fields
from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="DataFrequency",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="频率代码"
                    ),
                ),
                ("name", models.Char<PERSON>ield(max_length=50, verbose_name="频率名称")),
                (
                    "frequency_type",
                    models.CharField(
                        choices=[
                            ("1min", "1分钟"),
                            ("5min", "5分钟"),
                            ("15min", "15分钟"),
                            ("30min", "30分钟"),
                            ("1hour", "1小时"),
                            ("daily", "日线"),
                            ("weekly", "周线"),
                            ("monthly", "月线"),
                        ],
                        max_length=20,
                        verbose_name="频率类型",
                    ),
                ),
                ("seconds", models.IntegerField(verbose_name="秒数")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
            ],
            options={
                "verbose_name": "数据频率",
                "verbose_name_plural": "数据频率",
                "ordering": ["seconds"],
            },
        ),
        migrations.CreateModel(
            name="DataSource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        max_length=100, unique=True, verbose_name="数据源名称"
                    ),
                ),
                ("description", models.TextField(blank=True, verbose_name="描述")),
                ("provider", models.CharField(max_length=100, verbose_name="提供商")),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
            ],
            options={
                "verbose_name": "数据源",
                "verbose_name_plural": "数据源",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Market",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "code",
                    models.CharField(
                        max_length=20, unique=True, verbose_name="市场代码"
                    ),
                ),
                ("name", models.CharField(max_length=100, verbose_name="市场名称")),
                (
                    "market_type",
                    models.CharField(
                        choices=[
                            ("A_STOCK", "A股"),
                            ("FUTURES", "期货"),
                            ("OPTIONS", "期权"),
                            ("BONDS", "债券"),
                            ("FOREX", "外汇"),
                            ("CRYPTO", "数字货币"),
                        ],
                        max_length=20,
                        verbose_name="市场类型",
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        default="Asia/Shanghai", max_length=50, verbose_name="时区"
                    ),
                ),
                (
                    "trading_hours",
                    models.JSONField(default=dict, verbose_name="交易时间"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
            ],
            options={
                "verbose_name": "市场",
                "verbose_name_plural": "市场",
                "ordering": ["code"],
            },
        ),
        migrations.CreateModel(
            name="Symbol",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("code", models.CharField(max_length=50, verbose_name="标的代码")),
                ("name", models.CharField(max_length=200, verbose_name="标的名称")),
                (
                    "industry",
                    models.CharField(blank=True, max_length=100, verbose_name="行业"),
                ),
                (
                    "sector",
                    models.CharField(blank=True, max_length=100, verbose_name="板块"),
                ),
                (
                    "list_date",
                    models.DateField(blank=True, null=True, verbose_name="上市日期"),
                ),
                (
                    "delist_date",
                    models.DateField(blank=True, null=True, verbose_name="退市日期"),
                ),
                (
                    "is_active",
                    models.BooleanField(default=True, verbose_name="是否激活"),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "market",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="findata.market",
                        verbose_name="所属市场",
                    ),
                ),
            ],
            options={
                "verbose_name": "交易标的",
                "verbose_name_plural": "交易标的",
                "ordering": ["market", "code"],
                "unique_together": {("code", "market")},
            },
        ),
        migrations.CreateModel(
            name="AStockDailyData",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "time",
                    timescale.db.models.fields.TimescaleDateTimeField(
                        interval="1 month", verbose_name="时间戳"
                    ),
                ),
                (
                    "open_price",
                    models.DecimalField(
                        decimal_places=6,
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="开盘价",
                    ),
                ),
                (
                    "high_price",
                    models.DecimalField(
                        decimal_places=6,
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="最高价",
                    ),
                ),
                (
                    "low_price",
                    models.DecimalField(
                        decimal_places=6,
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="最低价",
                    ),
                ),
                (
                    "close_price",
                    models.DecimalField(
                        decimal_places=6,
                        max_digits=20,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="收盘价",
                    ),
                ),
                (
                    "volume",
                    models.BigIntegerField(
                        validators=[django.core.validators.MinValueValidator(0)],
                        verbose_name="成交量",
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=20,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(Decimal("0"))
                        ],
                        verbose_name="成交额",
                    ),
                ),
                (
                    "pre_close",
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        max_digits=20,
                        null=True,
                        verbose_name="前收盘价",
                    ),
                ),
                (
                    "change",
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        max_digits=20,
                        null=True,
                        verbose_name="涨跌额",
                    ),
                ),
                (
                    "pct_change",
                    models.DecimalField(
                        blank=True,
                        decimal_places=6,
                        max_digits=10,
                        null=True,
                        verbose_name="涨跌幅(%)",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "data_source",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="findata.datasource",
                        verbose_name="数据源",
                    ),
                ),
                (
                    "symbol",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="findata.symbol",
                        verbose_name="交易标的",
                    ),
                ),
            ],
            options={
                "verbose_name": "A股日线数据",
                "verbose_name_plural": "A股日线数据",
                "db_table": "findata_a_stock_daily",
                "ordering": ["-time"],
                "unique_together": {("symbol", "time", "data_source")},
            },
        ),
    ]
