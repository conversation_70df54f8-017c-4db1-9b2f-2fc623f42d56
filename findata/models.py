from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from timescale.db.models.models import TimescaleModel
from timescale.db.models.fields import TimescaleDateTimeField
from timescale.db.models.managers import TimescaleManager


class DataSource(models.Model):
    """数据源"""
    name = models.CharField(max_length=100, unique=True, verbose_name="数据源名称")
    description = models.TextField(blank=True, verbose_name="描述")
    provider = models.CharField(max_length=100, verbose_name="提供商")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "数据源"
        verbose_name_plural = "数据源"
        ordering = ['name']

    def __str__(self):
        return self.name


class Market(models.Model):
    """市场信息"""
    MARKET_CHOICES = [
        ('A_STOCK', 'A股'),
        ('FUTURES', '期货'),
        ('OPTIONS', '期权'),
        ('BONDS', '债券'),
        ('FOREX', '外汇'),
        ('CRYPTO', '数字货币'),
    ]
    
    code = models.CharField(max_length=20, unique=True, verbose_name="市场代码")
    name = models.CharField(max_length=100, verbose_name="市场名称")
    market_type = models.CharField(max_length=20, choices=MARKET_CHOICES, verbose_name="市场类型")
    timezone = models.CharField(max_length=50, default='Asia/Shanghai', verbose_name="时区")
    trading_hours = models.JSONField(default=dict, verbose_name="交易时间")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "市场"
        verbose_name_plural = "市场"
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class Symbol(models.Model):
    """交易标的"""
    code = models.CharField(max_length=50, verbose_name="标的代码")
    name = models.CharField(max_length=200, verbose_name="标的名称")
    market = models.ForeignKey(Market, on_delete=models.CASCADE, verbose_name="所属市场")
    industry = models.CharField(max_length=100, blank=True, verbose_name="行业")
    sector = models.CharField(max_length=100, blank=True, verbose_name="板块")
    list_date = models.DateField(null=True, blank=True, verbose_name="上市日期")
    delist_date = models.DateField(null=True, blank=True, verbose_name="退市日期")
    is_active = models.BooleanField(default=True, verbose_name="是否激活")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "交易标的"
        verbose_name_plural = "交易标的"
        unique_together = [['code', 'market']]
        ordering = ['market', 'code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class DataFrequency(models.Model):
    """数据频率"""
    FREQUENCY_CHOICES = [
        ('1min', '1分钟'),
        ('5min', '5分钟'),
        ('15min', '15分钟'),
        ('30min', '30分钟'),
        ('1hour', '1小时'),
        ('daily', '日线'),
        ('weekly', '周线'),
        ('monthly', '月线'),
    ]
    
    code = models.CharField(max_length=20, unique=True, verbose_name="频率代码")
    name = models.CharField(max_length=50, verbose_name="频率名称")
    frequency_type = models.CharField(max_length=20, choices=FREQUENCY_CHOICES, verbose_name="频率类型")
    seconds = models.IntegerField(verbose_name="秒数")  # 用于计算和比较
    is_active = models.BooleanField(default=True, verbose_name="是否激活")

    class Meta:
        verbose_name = "数据频率"
        verbose_name_plural = "数据频率"
        ordering = ['seconds']

    def __str__(self):
        return self.name


class BaseMarketData(TimescaleModel):
    """市场行情数据基类 - 使用 TimescaleDB"""
    symbol = models.ForeignKey(Symbol, on_delete=models.CASCADE, verbose_name="交易标的")
    data_source = models.ForeignKey(DataSource, on_delete=models.CASCADE, verbose_name="数据源")
    
    # 时间字段 - TimescaleDB 的分区键，使用 TimescaleDateTimeField
    time = TimescaleDateTimeField(interval="1 month", verbose_name="时间戳")
    
    # OHLCV数据
    open_price = models.DecimalField(
        max_digits=20, decimal_places=6, 
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name="开盘价"
    )
    high_price = models.DecimalField(
        max_digits=20, decimal_places=6,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name="最高价"
    )
    low_price = models.DecimalField(
        max_digits=20, decimal_places=6,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name="最低价"
    )
    close_price = models.DecimalField(
        max_digits=20, decimal_places=6,
        validators=[MinValueValidator(Decimal('0'))],
        verbose_name="收盘价"
    )
    volume = models.BigIntegerField(
        validators=[MinValueValidator(0)],
        verbose_name="成交量"
    )
    amount = models.DecimalField(
        max_digits=20, decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        null=True, blank=True,
        verbose_name="成交额"
    )
    
    # 扩展字段
    pre_close = models.DecimalField(
        max_digits=20, decimal_places=6,
        null=True, blank=True,
        verbose_name="前收盘价"
    )
    change = models.DecimalField(
        max_digits=20, decimal_places=6,
        null=True, blank=True,
        verbose_name="涨跌额"
    )
    pct_change = models.DecimalField(
        max_digits=10, decimal_places=6,
        null=True, blank=True,
        verbose_name="涨跌幅(%)"
    )
    

    
    # 元数据
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    # 使用 TimescaleManager
    objects = TimescaleManager()

    class Meta:
        abstract = True
        ordering = ['-time']
        indexes = [
            models.Index(fields=['symbol', 'time']),
        ]

    def __str__(self):
        return f"{self.symbol.code} - {self.time}"

    @property
    def daily_return(self):
        """日收益率"""
        if self.pre_close and self.pre_close > 0:
            return (self.close_price - self.pre_close) / self.pre_close
        return None


class AStockDailyData(BaseMarketData):
    """A股日线数据"""
    
    class Meta:
        verbose_name = "A股日线数据"
        verbose_name_plural = "A股日线数据"
        db_table = 'findata_a_stock_daily'
        unique_together = [['symbol', 'time', 'data_source']]
        ordering = ['-time']


# 未来可以扩展其他市场和时间尺度的数据表
# 例如：
# class FuturesMinuteData(BaseMarketData):
#     """期货分钟数据"""
#     
#     # 重写 time 字段，使用更小的 chunk interval
#     time = TimescaleDateTimeField(interval="1 day", verbose_name="时间戳")
#     
#     class Meta:
#         verbose_name = "期货分钟数据"
#         verbose_name_plural = "期货分钟数据"
#         db_table = 'findata_futures_minute'
#         unique_together = [['symbol', 'time', 'data_source']]
#         ordering = ['-time']
