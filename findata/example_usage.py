"""
金融数据管理app使用示例

这个脚本演示如何使用findata app进行金融数据的管理和查询。
运行方式: python manage.py shell < findata/example_usage.py
或者在Django shell中导入并执行其中的函数。
"""

from datetime import datetime, timedelta
from decimal import Decimal
import random
from django.db import models

from findata.models import (
    DataSource, Market, Symbol, DataFrequency,
    MarketData, DataStatistics
)


def create_sample_symbols():
    """创建示例股票标的"""
    try:
        # 获取市场
        sse = Market.objects.get(code='SSE')
        szse = Market.objects.get(code='SZSE')
        
        # 获取数据源
        tushare = DataSource.objects.get(name='TuShare')
        
        # 创建示例股票
        symbols_data = [
            {'code': '000001.SZ', 'name': '平安银行', 'market': szse, 'industry': '银行', 'sector': '金融服务'},
            {'code': '000002.SZ', 'name': '万科A', 'market': szse, 'industry': '房地产开发', 'sector': '房地产'},
            {'code': '600000.SH', 'name': '浦发银行', 'market': sse, 'industry': '银行', 'sector': '金融服务'},
            {'code': '600036.SH', 'name': '招商银行', 'market': sse, 'industry': '银行', 'sector': '金融服务'},
            {'code': '600519.SH', 'name': '贵州茅台', 'market': sse, 'industry': '白酒', 'sector': '食品饮料'},
            {'code': '000858.SZ', 'name': '五粮液', 'market': szse, 'industry': '白酒', 'sector': '食品饮料'},
        ]
        
        created_symbols = []
        for symbol_data in symbols_data:
            symbol, created = Symbol.objects.get_or_create(
                code=symbol_data['code'],
                market=symbol_data['market'],
                defaults={
                    'name': symbol_data['name'],
                    'industry': symbol_data['industry'],
                    'sector': symbol_data['sector'],
                    'is_active': True
                }
            )
            if created:
                created_symbols.append(symbol)
                
        print(f"✅ 创建了 {len(created_symbols)} 个示例标的")
        return created_symbols
        
    except Exception as e:
        print(f"❌ 创建示例标的失败: {e}")
        return []


def generate_sample_market_data(symbol, days=30):
    """为指定标的生成示例市场数据"""
    try:
        # 获取日线频率和数据源
        daily_freq = DataFrequency.objects.get(code='daily')
        tushare = DataSource.objects.get(name='TuShare')
        
        # 生成过去30天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        market_data_list = []
        base_price = Decimal('10.00')  # 基础价格
        
        for i in range(days):
            current_date = start_date + timedelta(days=i)
            
            # 模拟价格波动
            change_pct = random.uniform(-0.05, 0.05)  # -5%到+5%的随机波动
            
            open_price = base_price * Decimal(1 + random.uniform(-0.02, 0.02))
            close_price = open_price * Decimal(1 + change_pct)
            high_price = max(open_price, close_price) * Decimal(1 + random.uniform(0, 0.03))
            low_price = min(open_price, close_price) * Decimal(1 - random.uniform(0, 0.03))
            
            volume = random.randint(1000000, 10000000)  # 100万到1000万成交量
            amount = close_price * volume
            
            market_data = MarketData(
                symbol=symbol,
                frequency=daily_freq,
                data_source=tushare,
                timestamp=current_date,
                open_price=round(open_price, 2),
                high_price=round(high_price, 2),
                low_price=round(low_price, 2),
                close_price=round(close_price, 2),
                volume=volume,
                amount=round(amount, 2),
                pre_close=base_price,
                change=round(close_price - base_price, 2),
                pct_change=round(change_pct * 100, 2)
            )
            market_data_list.append(market_data)
            
            # 更新基础价格为当日收盘价
            base_price = close_price
        
        # 批量创建数据
        MarketData.objects.bulk_create(market_data_list, ignore_conflicts=True)
        print(f"✅ 为 {symbol.code} 生成了 {len(market_data_list)} 条市场数据")
        
        return len(market_data_list)
        
    except Exception as e:
        print(f"❌ 生成市场数据失败: {e}")
        return 0


def create_data_statistics():
    """创建数据统计信息"""
    try:
        symbols = Symbol.objects.all()
        daily_freq = DataFrequency.objects.get(code='daily')
        tushare = DataSource.objects.get(name='TuShare')
        
        for symbol in symbols:
            # 获取该标的的数据统计
            market_data = MarketData.objects.filter(
                symbol=symbol,
                frequency=daily_freq,
                data_source=tushare
            )
            
            if market_data.exists():
                total_records = market_data.count()
                start_date = market_data.earliest('timestamp').timestamp
                end_date = market_data.latest('timestamp').timestamp
                
                # 计算数据质量分数（简单示例）
                expected_days = (end_date - start_date).days + 1
                quality_score = min(100.0, (total_records / expected_days) * 100)
                
                # 创建或更新统计信息
                stats, created = DataStatistics.objects.update_or_create(
                    symbol=symbol,
                    frequency=daily_freq,
                    data_source=tushare,
                    defaults={
                        'total_records': total_records,
                        'start_date': start_date,
                        'end_date': end_date,
                        'last_update': datetime.now(),
                        'missing_days': max(0, expected_days - total_records),
                        'data_quality_score': quality_score
                    }
                )
                
                if created:
                    print(f"✅ 为 {symbol.code} 创建统计信息")
                    
    except Exception as e:
        print(f"❌ 创建数据统计失败: {e}")


def query_examples():
    """查询示例"""
    print("\n=== 查询示例 ===")
    
    # 1. 查询所有激活的标的
    active_symbols = Symbol.objects.filter(is_active=True)
    print(f"📊 激活标的数量: {active_symbols.count()}")
    
    # 2. 按市场分组统计
    markets = Market.objects.all()
    for market in markets:
        symbol_count = Symbol.objects.filter(market=market, is_active=True).count()
        data_count = MarketData.objects.filter(symbol__market=market).count()
        print(f"🏪 {market.name}: {symbol_count} 个标的, {data_count} 条数据")
    
    # 3. 查询最新数据
    latest_data = MarketData.objects.select_related('symbol').order_by('-timestamp')[:5]
    print(f"\n📈 最新5条数据:")
    for data in latest_data:
        print(f"  {data.symbol.code} - {data.close_price} ({data.pct_change:+.2f}%)")
    
    # 4. 数据质量统计
    avg_quality = DataStatistics.objects.aggregate(
        avg_quality=models.Avg('data_quality_score')
    )['avg_quality']
    print(f"\n📋 平均数据质量分数: {avg_quality:.2f}")


def main():
    """主函数 - 运行所有示例"""
    print("🚀 开始运行金融数据管理示例...")
    
    # 1. 创建示例标的
    symbols = create_sample_symbols()
    
    # 2. 为每个标的生成示例数据
    if symbols:
        total_records = 0
        for symbol in symbols:
            records = generate_sample_market_data(symbol, days=30)
            total_records += records
        print(f"📊 总共生成 {total_records} 条市场数据")
    
    # 3. 创建数据统计
    create_data_statistics()
    
    # 4. 运行查询示例
    query_examples()
    
    print("\n✅ 示例运行完成！")
    print("💡 现在可以访问以下API接口:")
    print("   - GET /findata/overview - 数据概览")
    print("   - GET /findata/symbols - 标的列表")
    print("   - GET /findata/market-data - 市场数据")
    print("   - GET /admin/ - 管理后台")


if __name__ == "__main__":
    main() 