import time
import pandas as pd
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from findata.models import DataSource, Market, Symbol, AStockDailyData
import akshare as ak


class Command(BaseCommand):
    help = '从 AKShare 获取股票数据并写入数据库'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start-date',
            type=str,
            required=True,
            help='开始日期 (格式: YYYY-MM-DD)',
        )
        parser.add_argument(
            '--end-date',
            type=str,
            required=True,
            help='结束日期 (格式: YYYY-MM-DD)',
        )
        parser.add_argument(
            '--symbol',
            type=str,
            help='特定标的代码 (例如: 000001.SZ)，不指定则获取所有标的数据',
        )
        parser.add_argument(
            '--market',
            type=str,
            choices=['SSE', 'SZSE', 'BJE'],
            help='指定市场 (SSE/SZSE/BJE)，不指定则获取所有A股市场数据',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='批量写入数据库的大小 (默认: 100)',
        )
        parser.add_argument(
            '--delay',
            type=float,
            default=0.5,
            help='请求之间的延迟时间（秒）(默认: 0.5)',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制更新已存在的数据',
        )
        parser.add_argument(
            '--update-symbols',
            action='store_true',
            help='从 AKShare 更新股票列表到数据库',
        )

    def handle(self, *args, **options):
        start_date = options['start_date']
        end_date = options['end_date']
        symbol_code = options['symbol']
        market_code = options['market']
        batch_size = options['batch_size']
        delay = options['delay']
        force = options['force']
        update_symbols = options['update_symbols']

        # 验证日期格式
        try:
            start_dt = datetime.strptime(start_date, '%Y-%m-%d')
            end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            raise CommandError('日期格式错误，请使用 YYYY-MM-DD 格式')

        if start_dt > end_dt:
            raise CommandError('开始日期不能晚于结束日期')

        # 获取 AKShare 数据源
        try:
            data_source = DataSource.objects.get(name='AKShare')
        except DataSource.DoesNotExist:
            raise CommandError('AKShare 数据源不存在，请先运行 init_findata 命令')

        # 如果需要更新股票列表
        if update_symbols:
            self.update_symbols_from_akshare(data_source, delay)

        # 获取要处理的标的
        symbols = self.get_symbols_to_process(symbol_code, market_code, update_symbols)
        
        if not symbols:
            self.stdout.write(
                self.style.WARNING('没有找到符合条件的标的')
            )
            return

        self.stdout.write(f'准备获取 {len(symbols)} 个标的的数据')
        self.stdout.write(f'时间范围: {start_date} 到 {end_date}')
        self.stdout.write(f'请求延迟: {delay} 秒')

        # 处理每个标的
        success_count = 0
        error_count = 0
        
        for i, symbol in enumerate(symbols, 1):
            self.stdout.write(f'\n[{i}/{len(symbols)}] 处理标的: {symbol.code} - {symbol.name}')
            
            try:
                # 获取数据
                data_count = self.fetch_symbol_data(
                    symbol, data_source, start_date, end_date, batch_size, force
                )
                
                if data_count > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'  ✅ 成功获取 {data_count} 条数据')
                    )
                    success_count += 1
                else:
                    self.stdout.write(
                        self.style.WARNING(f'  ⚠️ 无新数据或数据已存在')
                    )
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'  ❌ 错误: {str(e)}')
                )
                error_count += 1
            
            # 添加延迟，防止被ban
            if i < len(symbols):  # 最后一个不需要延迟
                time.sleep(delay)

        # 输出总结
        self.stdout.write(f'\n=== 数据获取完成 ===')
        self.stdout.write(f'成功处理: {success_count} 个标的')
        self.stdout.write(f'失败处理: {error_count} 个标的')
        
        if error_count == 0:
            self.stdout.write(
                self.style.SUCCESS('🎉 所有数据获取完成！')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'⚠️ 部分数据获取失败，请检查错误信息')
            )

    def update_symbols_from_akshare(self, data_source, delay):
        """从 AKShare 更新股票列表到数据库"""
        self.stdout.write('正在从 AKShare 获取股票列表...')
        
        try:
            # 获取股票列表
            stock_list = ak.stock_info_a_code_name()
            self.stdout.write(f'获取到 {len(stock_list)} 只股票')
            
            # 获取市场信息
            sse_market = Market.objects.get(code='SSE')
            szse_market = Market.objects.get(code='SZSE')
            bje_market = Market.objects.get(code='BJE')
            
            created_count = 0
            updated_count = 0
            
            for _, row in stock_list.iterrows():
                code = row['code']
                name = row['name']
                
                # 根据代码判断市场
                if code.startswith('60') or code.startswith('68'):
                    market = sse_market
                    full_code = f'{code}.SH'
                elif code.startswith('00') or code.startswith('30'):
                    market = szse_market
                    full_code = f'{code}.SZ'
                elif code.startswith('43') or code.startswith('83') or code.startswith('87'):
                    market = bje_market
                    full_code = f'{code}.BJ'
                else:
                    # 跳过无法识别的股票代码
                    continue
                
                # 创建或更新股票
                symbol, created = Symbol.objects.get_or_create(
                    code=full_code,
                    market=market,
                    defaults={
                        'name': name,
                        'is_active': True
                    }
                )
                
                if created:
                    created_count += 1
                else:
                    # 更新名称（可能有变化）
                    if symbol.name != name:
                        symbol.name = name
                        symbol.save()
                        updated_count += 1
                
                # 添加延迟，避免过于频繁的数据库操作
                if (created_count + updated_count) % 100 == 0:
                    time.sleep(delay / 10)  # 减少延迟，因为这是数据库操作
            
            self.stdout.write(
                self.style.SUCCESS(f'股票列表更新完成！新增: {created_count}, 更新: {updated_count}')
            )
            
        except Exception as e:
            raise CommandError(f'更新股票列表失败: {str(e)}')

    def get_symbols_to_process(self, symbol_code, market_code, update_symbols=False):
        """获取要处理的标的列表"""
        if symbol_code:
            # 处理特定标的
            try:
                symbol = Symbol.objects.get(code=symbol_code)
                return [symbol]
            except Symbol.DoesNotExist:
                raise CommandError(f'标的 {symbol_code} 不存在')
        else:
            # 获取所有A股标的
            queryset = Symbol.objects.filter(
                market__market_type='A_STOCK',
                is_active=True
            )
            
            if market_code:
                queryset = queryset.filter(market__code=market_code)
            
            symbols = list(queryset.order_by('code'))
            
            # 如果没有股票数据且没有指定更新标的，提示用户
            if not symbols and not update_symbols:
                raise CommandError(
                    '数据库中没有股票数据，请先运行 --update-symbols 参数来获取股票列表'
                )
            
            return symbols

    def fetch_symbol_data(self, symbol, data_source, start_date, end_date, batch_size, force):
        """获取单个标的的数据"""
        # 转换标的代码格式 (从 000001.SZ 转换为 sz000001)
        ak_symbol = self.convert_symbol_code(symbol.code)
        
        # 检查是否已有数据（如果不是强制模式）
        if not force:
            existing_count = AStockDailyData.objects.filter(
                symbol=symbol,
                data_source=data_source,
                time__date__range=[start_date, end_date]
            ).count()
            
            if existing_count > 0:
                return 0  # 已有数据，跳过

        try:
            # 从 AKShare 获取数据
            df = ak.stock_zh_a_daily(
                symbol=ak_symbol,
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', ''),
                adjust="qfq"  # 前复权
            )
            
            if df.empty:
                return 0

            # 转换并保存数据
            return self.save_data_to_db(df, symbol, data_source, batch_size, force)
            
        except Exception as e:
            raise Exception(f'获取数据失败: {str(e)}')

    def convert_symbol_code(self, symbol_code):
        """转换标的代码格式"""
        # 从 000001.SZ 转换为 sz000001
        # 从 600000.SH 转换为 sh600000
        if symbol_code.endswith('.SZ'):
            return 'sz' + symbol_code[:6]
        elif symbol_code.endswith('.SH'):
            return 'sh' + symbol_code[:6]
        else:
            raise ValueError(f'不支持的标的代码格式: {symbol_code}')

    def save_data_to_db(self, df, symbol, data_source, batch_size, force):
        """保存数据到数据库"""
        # 检查数据格式
        expected_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'amount']
        if not all(col in df.columns for col in expected_columns):
            raise ValueError(f'数据格式错误，缺少必要的列: {expected_columns}')
        
        # 转换数据类型
        df['date'] = pd.to_datetime(df['date'])
        df['open'] = pd.to_numeric(df['open'], errors='coerce')
        df['high'] = pd.to_numeric(df['high'], errors='coerce')
        df['low'] = pd.to_numeric(df['low'], errors='coerce')
        df['close'] = pd.to_numeric(df['close'], errors='coerce')
        df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)
        df['amount'] = pd.to_numeric(df['amount'], errors='coerce')
        
        # 删除无效数据
        df = df.dropna(subset=['open', 'high', 'low', 'close'])
        
        if df.empty:
            return 0

        # 按日期排序
        df = df.sort_values('date')
        
        # 计算前收盘价、涨跌额和涨跌幅
        df['pre_close'] = df['close'].shift(1)
        df['change'] = df['close'] - df['pre_close']
        df['pct_change'] = (df['change'] / df['pre_close'] * 100).round(2)
        
        # 第一天没有前收盘价，设置为空
        df.loc[df.index[0], ['pre_close', 'change', 'pct_change']] = None

        # 如果强制模式，先删除已有数据
        if force:
            AStockDailyData.objects.filter(
                symbol=symbol,
                data_source=data_source,
                time__date__in=df['date'].dt.date
            ).delete()

        # 批量创建数据
        batch_data = []
        saved_count = 0
        
        for _, row in df.iterrows():
            data_record = AStockDailyData(
                symbol=symbol,
                data_source=data_source,
                time=timezone.make_aware(
                    datetime.combine(row['date'].date(), datetime.min.time())
                ),
                open_price=row['open'],
                high_price=row['high'],
                low_price=row['low'],
                close_price=row['close'],
                volume=int(row['volume']),
                amount=row['amount'],
                pre_close=row['pre_close'] if pd.notna(row['pre_close']) else None,
                change=row['change'] if pd.notna(row['change']) else None,
                pct_change=row['pct_change'] if pd.notna(row['pct_change']) else None,
            )
            
            batch_data.append(data_record)
            
            # 批量保存
            if len(batch_data) >= batch_size:
                with transaction.atomic():
                    AStockDailyData.objects.bulk_create(
                        batch_data,
                        ignore_conflicts=not force
                    )
                saved_count += len(batch_data)
                batch_data = []

        # 保存剩余数据
        if batch_data:
            with transaction.atomic():
                AStockDailyData.objects.bulk_create(
                    batch_data,
                    ignore_conflicts=not force
                )
            saved_count += len(batch_data)

        return saved_count 