from django.core.management.base import BaseCommand
from django.db import transaction
from findata.models import DataSource, Market, DataFrequency


class Command(BaseCommand):
    help = '初始化金融数据基础配置数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制重新创建数据（会删除现有数据）',
        )
        parser.add_argument(
            '--with-samples',
            action='store_true',
            help='创建示例交易标的数据',
        )

    def handle(self, *args, **options):
        force = options['force']
        with_samples = options['with_samples']
        
        if force:
            self.stdout.write(
                self.style.WARNING('强制模式：将删除现有基础数据...')
            )
            with transaction.atomic():
                # 需要按依赖顺序删除，Symbol 依赖 Market，所以先删除 Symbol
                from findata.models import Symbol
                Symbol.objects.all().delete()
                DataFrequency.objects.all().delete()
                Market.objects.all().delete()
                DataSource.objects.all().delete()

        # 创建数据源
        self.create_data_sources()
        
        # 创建市场
        self.create_markets()
        
        # 创建数据频率
        self.create_frequencies()
        
        # 创建示例标的（如果指定了参数）
        if with_samples:
            self.create_sample_symbols()
        
        self.stdout.write(
            self.style.SUCCESS('✅ 金融数据基础配置初始化完成！')
        )

    def create_data_sources(self):
        """创建数据源"""
        sources = [
            {
                'name': 'TuShare',
                'description': 'TuShare财经数据接口',
                'provider': 'TuShare'
            },
            {
                'name': 'AKShare',
                'description': 'AKShare开源财经数据接口',
                'provider': 'AKTools'
            },
            {
                'name': 'Wind万得',
                'description': 'Wind万得金融终端',
                'provider': 'Wind'
            },
            {
                'name': '东方财富',
                'description': '东方财富数据接口',
                'provider': '东方财富'
            },
            {
                'name': '新浪财经',
                'description': '新浪财经数据接口',
                'provider': '新浪'
            },
        ]
        
        created_count = 0
        for source_data in sources:
            source, created = DataSource.objects.get_or_create(
                name=source_data['name'],
                defaults={
                    'description': source_data['description'],
                    'provider': source_data['provider'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                
        self.stdout.write(f'📊 创建了 {created_count} 个数据源')

    def create_markets(self):
        """创建市场"""
        markets = [
            {
                'code': 'SSE',
                'name': '上海证券交易所',
                'market_type': 'A_STOCK',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'morning': {'start': '09:30', 'end': '11:30'},
                    'afternoon': {'start': '13:00', 'end': '15:00'}
                }
            },
            {
                'code': 'SZSE',
                'name': '深圳证券交易所',
                'market_type': 'A_STOCK',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'morning': {'start': '09:30', 'end': '11:30'},
                    'afternoon': {'start': '13:00', 'end': '15:00'}
                }
            },
            {
                'code': 'BJE',
                'name': '北京证券交易所',
                'market_type': 'A_STOCK',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'morning': {'start': '09:30', 'end': '11:30'},
                    'afternoon': {'start': '13:00', 'end': '15:00'}
                }
            },
            {
                'code': 'SHFE',
                'name': '上海期货交易所',
                'market_type': 'FUTURES',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'day': {'start': '09:00', 'end': '15:00'},
                    'night': {'start': '21:00', 'end': '23:00'}
                }
            },
            {
                'code': 'DCE',
                'name': '大连商品交易所',
                'market_type': 'FUTURES',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'day': {'start': '09:00', 'end': '15:00'},
                    'night': {'start': '21:00', 'end': '23:00'}
                }
            },
            {
                'code': 'CZCE',
                'name': '郑州商品交易所',
                'market_type': 'FUTURES',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'day': {'start': '09:00', 'end': '15:00'},
                    'night': {'start': '21:00', 'end': '23:00'}
                }
            },
            {
                'code': 'CFFEX',
                'name': '中国金融期货交易所',
                'market_type': 'FUTURES',
                'timezone': 'Asia/Shanghai',
                'trading_hours': {
                    'morning': {'start': '09:30', 'end': '11:30'},
                    'afternoon': {'start': '13:00', 'end': '15:00'}
                }
            },
        ]
        
        created_count = 0
        for market_data in markets:
            market, created = Market.objects.get_or_create(
                code=market_data['code'],
                defaults={
                    'name': market_data['name'],
                    'market_type': market_data['market_type'],
                    'timezone': market_data['timezone'],
                    'trading_hours': market_data['trading_hours'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                
        self.stdout.write(f'🏪 创建了 {created_count} 个市场')

    def create_frequencies(self):
        """创建数据频率"""
        frequencies = [
            {
                'code': '1min',
                'name': '1分钟',
                'frequency_type': '1min',
                'seconds': 60
            },
            {
                'code': '5min',
                'name': '5分钟',
                'frequency_type': '5min',
                'seconds': 300
            },
            {
                'code': '15min',
                'name': '15分钟',
                'frequency_type': '15min',
                'seconds': 900
            },
            {
                'code': '30min',
                'name': '30分钟',
                'frequency_type': '30min',
                'seconds': 1800
            },
            {
                'code': '1hour',
                'name': '1小时',
                'frequency_type': '1hour',
                'seconds': 3600
            },
            {
                'code': 'daily',
                'name': '日线',
                'frequency_type': 'daily',
                'seconds': 86400
            },
            {
                'code': 'weekly',
                'name': '周线',
                'frequency_type': 'weekly',
                'seconds': 604800
            },
            {
                'code': 'monthly',
                'name': '月线',
                'frequency_type': 'monthly',
                'seconds': 2592000
            },
        ]
        
        created_count = 0
        for freq_data in frequencies:
            freq, created = DataFrequency.objects.get_or_create(
                code=freq_data['code'],
                defaults={
                    'name': freq_data['name'],
                    'frequency_type': freq_data['frequency_type'],
                    'seconds': freq_data['seconds'],
                    'is_active': True
                }
            )
            if created:
                created_count += 1
                
        self.stdout.write(f'⏰ 创建了 {created_count} 个数据频率')

    def create_sample_symbols(self):
        """创建示例标的（可选）"""
        from findata.models import Symbol
        
        # 获取上海证券交易所
        try:
            sse = Market.objects.get(code='SSE')
            szse = Market.objects.get(code='SZSE')
            
            sample_symbols = [
                {
                    'code': '000001.SZ',
                    'name': '平安银行',
                    'market': szse,
                    'industry': '银行',
                    'sector': '金融服务'
                },
                {
                    'code': '000002.SZ',
                    'name': '万科A',
                    'market': szse,
                    'industry': '房地产开发',
                    'sector': '房地产'
                },
                {
                    'code': '600000.SH',
                    'name': '浦发银行',
                    'market': sse,
                    'industry': '银行',
                    'sector': '金融服务'
                },
                {
                    'code': '600036.SH',
                    'name': '招商银行',
                    'market': sse,
                    'industry': '银行',
                    'sector': '金融服务'
                },
            ]
            
            created_count = 0
            for symbol_data in sample_symbols:
                symbol, created = Symbol.objects.get_or_create(
                    code=symbol_data['code'],
                    market=symbol_data['market'],
                    defaults={
                        'name': symbol_data['name'],
                        'industry': symbol_data['industry'],
                        'sector': symbol_data['sector'],
                        'is_active': True
                    }
                )
                if created:
                    created_count += 1
                    
            self.stdout.write(f'📈 创建了 {created_count} 个示例标的')
            
        except Market.DoesNotExist:
            self.stdout.write(
                self.style.WARNING('市场数据不存在，跳过创建示例标的')
            ) 