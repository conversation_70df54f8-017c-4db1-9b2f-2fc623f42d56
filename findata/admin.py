from django.contrib import admin
from django.db.models import <PERSON>, <PERSON>, Min
from django.utils.html import format_html
from django.urls import reverse
from django.utils.safestring import mark_safe

from .models import (
    DataSource, Market, Symbol, DataFrequency,
    AStockDailyData
)


@admin.register(DataSource)
class DataSourceAdmin(admin.ModelAdmin):
    list_display = ['name', 'provider', 'is_active', 'created_at']
    list_filter = ['is_active', 'provider', 'created_at']
    search_fields = ['name', 'provider', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'provider', 'description', 'is_active')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Market)
class MarketAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'market_type', 'timezone', 'is_active', 'symbols_count']
    list_filter = ['market_type', 'is_active', 'timezone']
    search_fields = ['code', 'name']
    readonly_fields = ['created_at']
    
    def symbols_count(self, obj):
        count = obj.symbol_set.filter(is_active=True).count()
        if count > 0:
            url = reverse('admin:findata_symbol_changelist') + f'?market__id__exact={obj.id}'
            return format_html('<a href="{}">{} 个标的</a>', url, count)
        return '0 个标的'
    symbols_count.short_description = '标的数量'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('code', 'name', 'market_type', 'is_active')
        }),
        ('交易设置', {
            'fields': ('timezone', 'trading_hours')
        }),
        ('时间信息', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )


@admin.register(Symbol)
class SymbolAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'market', 'industry', 'sector', 'is_active', 'data_records_count']
    list_filter = ['market', 'is_active', 'industry', 'sector', 'list_date']
    search_fields = ['code', 'name', 'industry', 'sector']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'list_date'
    
    def data_records_count(self, obj):
        # 根据标的所属市场显示不同的数据记录数
        if obj.market.market_type == 'A_STOCK':
            count = obj.astockdailydata_set.count()
            if count > 0:
                url = reverse('admin:findata_astockdailydata_changelist') + f'?symbol__id__exact={obj.id}'
                return format_html('<a href="{}">{:,} 条记录</a>', url, count)
        return '0 条记录'
    data_records_count.short_description = '数据记录'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('code', 'name', 'market', 'is_active')
        }),
        ('分类信息', {
            'fields': ('industry', 'sector')
        }),
        ('上市信息', {
            'fields': ('list_date', 'delist_date')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(DataFrequency)
class DataFrequencyAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'frequency_type', 'seconds', 'is_active']
    list_filter = ['frequency_type', 'is_active']
    search_fields = ['code', 'name']
    ordering = ['seconds']


@admin.register(AStockDailyData)
class AStockDailyDataAdmin(admin.ModelAdmin):
    list_display = [
        'symbol_info', 'time', 'close_price', 
        'volume', 'pct_change_colored', 'data_source'
    ]
    list_filter = [
        'data_source', 'symbol__market', 
        'time', 'symbol__industry'
    ]
    search_fields = ['symbol__code', 'symbol__name']
    readonly_fields = ['created_at', 'updated_at']
    date_hierarchy = 'time'
    
    # 自定义查询集，优化性能
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'symbol', 'symbol__market', 'data_source'
        )
    
    def symbol_info(self, obj):
        return f"{obj.symbol.code} - {obj.symbol.name}"
    symbol_info.short_description = '标的'
    symbol_info.admin_order_field = 'symbol__code'
    
    def pct_change_colored(self, obj):
        if obj.pct_change is None:
            return '-'
        
        color = 'red' if obj.pct_change < 0 else 'green' if obj.pct_change > 0 else 'black'
        return format_html(
            '<span style="color: {};">{:.2f}%</span>',
            color,
            obj.pct_change
        )
    pct_change_colored.short_description = '涨跌幅'
    pct_change_colored.admin_order_field = 'pct_change'
    
    fieldsets = (
        ('基本信息', {
            'fields': ('symbol', 'data_source', 'time')
        }),
        ('价格信息', {
            'fields': (
                ('open_price', 'high_price'),
                ('low_price', 'close_price'),
                ('pre_close', 'change', 'pct_change')
            )
        }),
        ('成交信息', {
            'fields': ('volume', 'amount')
        }),
        ('时间信息', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


# 自定义Admin站点标题
admin.site.site_header = '量化平台金融数据管理'
admin.site.site_title = '金融数据管理'
admin.site.index_title = '欢迎使用金融数据管理系统'
