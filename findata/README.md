# 金融数据管理 (FinData) App

这个Django app专门用于管理金融数据，支持A股、期货等市场的日线、分钟线数据，使用TimescaleDB作为时间序列数据库。

## 功能特性

### 📊 数据管理
- **多数据源支持**: TuShare、AKShare、Wind万得、东方财富等
- **多市场支持**: A股(上海、深圳、北京交易所)、期货、期权等
- **多频率支持**: 1分钟、5分钟、15分钟、30分钟、1小时、日线、周线、月线
- **OHLCV数据**: 开高低收、成交量、成交额、涨跌幅等
- **技术指标**: 支持MA5、MA10、MA20等基础技术指标

### 🚀 TimescaleDB优化
- **自动分区**: 按时间自动分区，提升查询性能
- **数据压缩**: 自动压缩历史数据，节省存储空间
- **数据保留**: 自动清理过期数据
- **时间序列查询**: 优化的时间范围查询

### 🔌 API接口
- **数据概览**: 获取数据总体统计信息
- **标的管理**: 查询交易标的列表和详情
- **数据查询**: 支持多条件筛选的市场数据查询
- **数据质量**: 数据质量评估报告
- **实时数据**: 获取标的最新行情数据

## 安装配置

### 1. 数据库配置

首先确保已安装TimescaleDB，然后修改Django设置：

```python
# settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'quantization_platform',
        'USER': 'postgres',
        'PASSWORD': 'your_password',
        'HOST': 'localhost',
        'PORT': '5432',
        'OPTIONS': {
            'options': '-c search_path=public'
        }
    }
}

INSTALLED_APPS = [
    # ... 其他apps
    'findata',
]
```

### 2. 安装依赖

```bash
pip install psycopg2-binary
```

### 3. 数据库迁移

```bash
# 创建迁移文件
python manage.py makemigrations findata

# 执行迁移（会自动设置TimescaleDB）
python manage.py migrate findata
```

### 4. 初始化基础数据

```bash
# 初始化数据源、市场、频率等基础配置
python manage.py init_findata

# 强制重新创建（会删除现有数据）
python manage.py init_findata --force
```

## API使用

### 数据概览
```
GET /findata/overview
```

### 获取所有数据源
```
GET /findata/data-sources
```

### 获取市场列表
```
GET /findata/markets
```

### 获取交易标的
```
GET /findata/symbols?market_code=SSE
```

### 查询市场数据
```
GET /findata/market-data?symbol_code=000001.SZ&start_date=2024-01-01&end_date=2024-12-31
```

### 标的数据汇总
```
GET /findata/symbols/summary?market_code=SSE
```

### 数据质量报告
```
GET /findata/data-quality
```

### 获取最新数据
```
GET /findata/latest-data/000001.SZ?frequency_code=daily
```

## 数据模型

### DataSource (数据源)
- 管理不同的数据提供商
- 支持启用/禁用状态

### Market (市场)
- 定义交易市场信息
- 包含交易时间、时区等配置

### Symbol (交易标的)
- 股票、期货等交易品种
- 包含行业、板块分类信息

### DataFrequency (数据频率)
- 定义数据的时间间隔
- 用于区分分钟线、日线等

### MarketData (市场数据)
- 核心的OHLCV时间序列数据
- 使用TimescaleDB优化存储和查询

### DataStatistics (数据统计)
- 数据质量和统计信息
- 用于监控数据完整性

## 管理后台

Django Admin已配置完整的管理界面：

- 数据源管理
- 市场配置
- 标的管理（支持批量操作）
- 市场数据查看（带颜色编码的涨跌幅显示）
- 数据统计监控

## 性能优化

### 索引策略
- 时间戳索引
- 复合索引 (symbol + frequency + timestamp)
- 标的时间索引 (symbol + timestamp)

### TimescaleDB特性
- 按周分区数据（可调整）
- 自动压缩7天前数据
- 保留2年历史数据
- 按标的和频率分段压缩

### 查询优化
- 使用select_related优化关联查询
- 分页查询避免大结果集
- 时间范围查询优化

## 数据导入

可以通过以下方式导入数据：

1. **Django ORM**
```python
from findata.models import MarketData, Symbol, DataFrequency, DataSource

# 批量创建数据
MarketData.objects.bulk_create([...])
```

2. **管理命令**
可以创建自定义管理命令来导入特定数据源的数据

3. **API接口**
通过REST API批量上传数据

## 注意事项

1. **TimescaleDB要求**: 确保PostgreSQL已安装TimescaleDB扩展
2. **数据一致性**: 使用事务确保数据导入的一致性
3. **性能监控**: 大量数据导入时注意监控系统性能
4. **备份策略**: 定期备份重要的金融数据

## 扩展开发

### 添加新的技术指标
在MarketData模型中添加新字段，然后创建迁移：

```python
# 在models.py中添加
rsi = models.DecimalField(max_digits=10, decimal_places=4, null=True, blank=True, verbose_name="RSI")
```

### 自定义数据源
继承DataSource模型或创建新的数据导入逻辑。

### 时间聚合查询
利用TimescaleDB的时间聚合函数进行高性能分析：

```python
from django.db import connection

def get_daily_ohlc(symbol_code, start_date, end_date):
    with connection.cursor() as cursor:
        cursor.execute("""
            SELECT 
                time_bucket('1 day', timestamp) as day,
                first(open_price, timestamp) as open,
                max(high_price) as high,
                min(low_price) as low,
                last(close_price, timestamp) as close,
                sum(volume) as volume
            FROM findata_market_data
            WHERE symbol_id = %s 
            AND timestamp BETWEEN %s AND %s
            GROUP BY day
            ORDER BY day
        """, [symbol_id, start_date, end_date])
        return cursor.fetchall()
```

这个app为量化平台提供了完整的金融数据管理解决方案，支持高性能的时间序列数据存储和查询。 