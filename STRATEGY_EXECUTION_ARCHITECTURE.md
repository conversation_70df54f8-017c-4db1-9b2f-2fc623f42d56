# 策略执行架构设计与实现

## 项目概述

本项目实现了一个基于Django和Podman容器的策略执行架构，为量化交易平台提供了隔离、可扩展的策略回测环境。

## 架构特点

### 🔒 **隔离执行**
- 每个策略在独立的Podman容器中执行
- 避免策略间相互影响
- 提供安全的执行环境

### 📦 **工作空间挂载**
- 当前工作空间挂载到容器内
- 策略代码和数据实时同步
- 支持动态代码更新

### 🎯 **任务跟踪**
- 容器ID作为任务标识符
- 完整的任务生命周期管理
- 实时状态同步和监控

### 🔄 **自动化管理**
- 自动容器清理机制
- 后台监控服务
- 错误恢复和重试

## 核心组件

### 1. Django管理命令
**文件**: `strategies/management/commands/run_strategy.py`

策略执行的入口点，支持：
- 策略ID和数据源参数
- 多种输出格式（JSON/文本）
- 完整的参数验证
- 详细的错误处理

### 2. 容器编排服务
**文件**: `strategies/container_orchestrator.py`

管理Podman容器的核心服务：
- 容器创建和生命周期管理
- 工作空间挂载和环境配置
- 状态查询和日志收集
- 资源限制和安全配置

### 3. 任务管理API
**文件**: `strategies/api.py` (新增端点)

RESTful API端点：
- `POST /execution-tasks` - 创建执行任务
- `GET /execution-tasks` - 获取任务列表
- `GET /execution-tasks/{id}` - 获取任务详情
- `GET /execution-tasks/{id}/status` - 查询任务状态
- `POST /execution-tasks/{id}/cancel` - 取消任务
- `GET /execution-tasks/{id}/logs` - 获取任务日志
- `GET /execution-tasks/stats` - 获取监控统计

### 4. 数据库模型
**文件**: `strategies/models.py`

新增 `StrategyExecutionTask` 模型：
- 完整的任务信息存储
- 容器ID和状态跟踪
- 执行参数和结果记录
- 时间戳和持续时间计算

### 5. 容器监控服务
**文件**: `strategies/container_monitor.py`

后台监控服务：
- 定期检查容器状态
- 自动同步任务状态
- 收集执行结果和日志
- 清理过期容器

### 6. 应用配置
**文件**: `strategies/apps.py`

Django应用配置：
- 自动启动监控服务
- 优雅关闭处理
- 错误恢复机制

## 部署文件

### 1. Dockerfile
**文件**: `Dockerfile.strategy`

策略执行容器镜像：
- 基于Python 3.12
- 包含所有必要依赖
- 非特权用户执行
- 安全配置

### 2. 依赖文件
**文件**: `requirements.txt`

项目依赖包：
- Django和相关扩展
- 数据库驱动
- 数据处理库
- 交易和回测框架

### 3. 数据库迁移
**文件**: `strategies/migrations/0002_strategyexecutiontask.py`

新增模型的数据库迁移文件

## 测试和验证

### 1. 集成测试命令
**文件**: `strategies/management/commands/test_container_execution.py`

完整的工作流测试：
- 创建测试策略
- 测试容器编排
- 验证API工作流
- 监控服务测试

### 2. 使用示例

```bash
# 测试完整工作流
python manage.py test_container_execution

# 跳过容器执行（开发环境）
python manage.py test_container_execution --skip-container

# 直接执行策略
python manage.py run_strategy --strategy-id 1 --symbols AAPL --start-date 2024-01-01 --end-date 2024-01-31
```

## API使用示例

### 创建执行任务
```bash
curl -X POST http://localhost:8000/strategies/execution-tasks \
  -H "Content-Type: application/json" \
  -d '{
    "strategy_id": 1,
    "name": "测试执行",
    "symbols": ["AAPL"],
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
  }'
```

### 查询任务状态
```bash
curl http://localhost:8000/strategies/execution-tasks/123/status
```

### 获取执行日志
```bash
curl http://localhost:8000/strategies/execution-tasks/123/logs
```

## 安全特性

1. **容器隔离**: 每个策略独立执行
2. **资源限制**: 内存和CPU限制
3. **非特权执行**: 容器内非root用户
4. **网络隔离**: 可配置网络策略
5. **代码沙箱**: 限制系统访问

## 监控和运维

1. **实时监控**: 自动状态同步
2. **日志收集**: 完整的执行日志
3. **错误处理**: 详细的错误报告
4. **自动清理**: 过期容器清理
5. **统计信息**: 监控仪表板

## 扩展性

1. **水平扩展**: 支持多节点部署
2. **负载均衡**: 容器分布式执行
3. **存储扩展**: 可插拔存储后端
4. **监控集成**: Prometheus/Grafana
5. **自定义镜像**: 灵活的容器配置

## 文档

- **详细文档**: `strategies/CONTAINER_EXECUTION_README.md`
- **API文档**: 通过Django Ninja自动生成
- **代码注释**: 完整的代码文档

## 总结

本架构成功实现了：

✅ **Django管理命令** - 策略执行入口点
✅ **容器编排服务** - Podman容器管理
✅ **任务管理API** - 完整的RESTful接口
✅ **数据库模型** - 任务状态跟踪
✅ **监控服务** - 自动化监控和清理
✅ **错误处理** - 完善的错误处理机制
✅ **集成测试** - 端到端测试验证

该架构提供了一个生产就绪的策略执行环境，支持隔离执行、实时监控、自动化管理和水平扩展。
